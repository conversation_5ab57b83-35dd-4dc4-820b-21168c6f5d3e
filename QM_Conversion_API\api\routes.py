import uuid
import asyncio
from typing import Dict, Any, Optional
from fastapi import <PERSON><PERSON>out<PERSON>, HTTPException, BackgroundTasks, Depends
from fastapi.responses import JSONResponse

from models.api_models import (
    MigrationRequest,
    MigrationResponse,
    MigrationStatus,
    MigrationResult,
    HealthResponse
)
from services.migration_service import MigrationService
from core.config import ConfigManager

# Create router
migration_router = APIRouter()

# In-memory storage for job status (in production, use Redis or database)
job_storage: Dict[str, Dict[str, Any]] = {}

def get_config_manager() -> ConfigManager:
    """Dependency to get configuration manager."""
    config_manager = ConfigManager()
    config_manager.load_from_env()
    return config_manager

def get_migration_service(config_manager: ConfigManager = Depends(get_config_manager)) -> MigrationService:
    """Dependency to get migration service."""
    return MigrationService(config_manager)

@migration_router.post("/migrate/start", response_model=MigrationResponse)
async def start_migration(
    request: MigrationRequest,
    background_tasks: BackgroundTasks,
    migration_service: MigrationService = Depends(get_migration_service)
):
    """
    Start a new database migration workflow.
    
    This endpoint initiates the AI-powered migration process with the provided
    source code, target code, and deployment error. The migration runs in the
    background and can be monitored using the job ID.
    
    **Workflow Steps:**
    1. Split SQL statements for granular analysis
    2. Identify problematic target statements using hybrid AI/position-based approach
    3. Map target statements to corresponding source statements
    4. Convert statements using AI with source context
    5. Validate conversions and deploy to target database
    6. Iterate until successful deployment
    
    **Returns:**
    - job_id: Unique identifier for tracking the migration
    - status: Current status of the migration
    - message: Human-readable status message
    """
    try:
        # Generate unique job ID
        job_id = str(uuid.uuid4())
        
        # Initialize job status
        job_storage[job_id] = {
            "status": "started",
            "message": "Migration workflow initiated",
            "progress": 0,
            "current_step": "initialization",
            "result": None,
            "error": None,
            "created_at": asyncio.get_event_loop().time()
        }
        
        # Start migration in background
        background_tasks.add_task(
            run_migration_workflow,
            job_id,
            request,
            migration_service
        )
        
        return MigrationResponse(
            job_id=job_id,
            status="started",
            message="Migration workflow initiated successfully. Use the job_id to track progress."
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start migration: {str(e)}")

@migration_router.get("/migrate/status/{job_id}", response_model=MigrationStatus)
async def get_migration_status(job_id: str):
    """
    Get the current status of a migration job.
    
    **Parameters:**
    - job_id: The unique identifier returned when starting the migration
    
    **Returns:**
    - status: Current status (started, running, completed, failed)
    - message: Human-readable status message
    - progress: Progress percentage (0-100)
    - current_step: Current workflow step being executed
    - estimated_time_remaining: Estimated time to completion (if available)
    """
    if job_id not in job_storage:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job_data = job_storage[job_id]
    
    # Calculate estimated time remaining (simple estimation)
    estimated_time = None
    if job_data["status"] == "running" and job_data["progress"] > 0:
        elapsed_time = asyncio.get_event_loop().time() - job_data["created_at"]
        if job_data["progress"] > 0:
            total_estimated = elapsed_time * (100 / job_data["progress"])
            estimated_time = max(0, total_estimated - elapsed_time)
    
    return MigrationStatus(
        job_id=job_id,
        status=job_data["status"],
        message=job_data["message"],
        progress=job_data["progress"],
        current_step=job_data["current_step"],
        estimated_time_remaining=estimated_time
    )

@migration_router.get("/migrate/result/{job_id}", response_model=MigrationResult)
async def get_migration_result(job_id: str):
    """
    Get the final result of a completed migration job.
    
    **Parameters:**
    - job_id: The unique identifier returned when starting the migration
    
    **Returns:**
    - Complete migration results including:
      - Final deployment status
      - Iteration count
      - Updated target code
      - Error messages (if any)
      - Audit trail of all changes
    
    **Note:** This endpoint only returns data for completed jobs.
    """
    if job_id not in job_storage:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job_data = job_storage[job_id]
    
    if job_data["status"] not in ["completed", "failed"]:
        raise HTTPException(
            status_code=400, 
            detail=f"Job is still {job_data['status']}. Results are only available for completed jobs."
        )
    
    if job_data["status"] == "failed":
        return MigrationResult(
            job_id=job_id,
            success=False,
            error_message=job_data["error"],
            deployment_successful=False
        )
    
    result = job_data["result"]
    if not result:
        raise HTTPException(status_code=500, detail="No result data available")
    
    return MigrationResult(
        job_id=job_id,
        success=True,
        deployment_successful=result.get("deployment_successful", False),
        iteration_count=result.get("iteration_count", 0),
        updated_target_code=result.get("updated_target_code"),
        error_message=result.get("error_message"),
        final_state=result
    )

@migration_router.delete("/migrate/job/{job_id}")
async def delete_migration_job(job_id: str):
    """
    Delete a migration job and its associated data.
    
    **Parameters:**
    - job_id: The unique identifier of the job to delete
    
    **Returns:**
    - Confirmation message
    
    **Note:** This action cannot be undone.
    """
    if job_id not in job_storage:
        raise HTTPException(status_code=404, detail="Job not found")
    
    del job_storage[job_id]
    
    return {"message": f"Job {job_id} deleted successfully"}

@migration_router.get("/migrate/jobs")
async def list_migration_jobs():
    """
    List all migration jobs with their current status.
    
    **Returns:**
    - List of all jobs with basic information:
      - job_id
      - status
      - progress
      - created_at
    
    **Note:** This endpoint is useful for monitoring multiple migrations.
    """
    jobs = []
    for job_id, job_data in job_storage.items():
        jobs.append({
            "job_id": job_id,
            "status": job_data["status"],
            "progress": job_data["progress"],
            "current_step": job_data["current_step"],
            "created_at": job_data["created_at"]
        })
    
    return {"jobs": jobs, "total_count": len(jobs)}

async def run_migration_workflow(job_id: str, request: MigrationRequest, migration_service: MigrationService):
    """
    Background task to run the migration workflow.
    
    This function executes the complete migration workflow and updates
    the job status throughout the process.
    """
    try:
        # Update status to running
        job_storage[job_id].update({
            "status": "running",
            "message": "Migration workflow is running",
            "progress": 10,
            "current_step": "initializing_llm"
        })
        
        # Run the migration workflow
        result = await migration_service.run_migration_async(
            source_code=request.source_code,
            target_code=request.target_code,
            deployment_error=request.deployment_error,
            progress_callback=lambda step, progress, message: update_job_progress(job_id, step, progress, message)
        )
        
        # Update final status
        job_storage[job_id].update({
            "status": "completed",
            "message": "Migration workflow completed successfully",
            "progress": 100,
            "current_step": "completed",
            "result": result
        })
        
    except Exception as e:
        # Update error status
        job_storage[job_id].update({
            "status": "failed",
            "message": f"Migration workflow failed: {str(e)}",
            "error": str(e),
            "current_step": "failed"
        })

def update_job_progress(job_id: str, step: str, progress: int, message: str):
    """Update job progress during workflow execution."""
    if job_id in job_storage:
        job_storage[job_id].update({
            "progress": progress,
            "current_step": step,
            "message": message
        })
