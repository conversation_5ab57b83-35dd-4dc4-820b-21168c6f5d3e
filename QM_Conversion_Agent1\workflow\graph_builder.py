import uuid
from typing import Dict, Any
from langgraph.graph import <PERSON><PERSON><PERSON>h, END, START
from langgraph.checkpoint.memory import MemorySaver
from state import WorkflowState
from nodes.conversion_nodes import UniversalCodeMigrationNodes
from langchain_core.runnables.graph import MermaidDrawMethod


class GraphBuilder:
    """
    Advanced workflow graph builder for database migration.

    This class constructs and manages a sophisticated AI-driven workflow graph that handles
    the complete database migration process. It orchestrates multiple nodes
    including error identification, source mapping, statement conversion, validation loops,
    and deployment verification using LangGraph for state management.

    Key Features:
        - Multi-node workflow with conditional routing
        - AI-driven error analysis and correction
        - Iterative improvement loops with validation
        - Real target database deployment testing
        - Comprehensive state management and audit trails
        - Support for complex SQL conversion scenarios

    Workflow Architecture:
        The graph implements a sophisticated flow with validation loops at each critical step,
        ensuring high-quality conversions through iterative AI improvement and real database testing.
    """

    def __init__(self, llm):
        """
        Initialize the workflow graph builder with AI language model integration.

        Sets up the core components for the database migration workflow including
        the LangGraph state management system, memory checkpointing for workflow persistence,
        and integration with the provided language model for AI-driven analysis.

        Args:
            llm: Initialized Language Model instance for AI-driven SQL analysis and conversion.
                 Supports multiple providers (OpenAI, Azure OpenAI, Anthropic, Groq, Gemini, Ollama)
                 configured for optimal database migration performance.

        Attributes:
            llm: The language model instance for AI operations
            builder: LangGraph StateGraph builder for workflow construction
            memory: MemorySaver for workflow state persistence and checkpointing
        """
        self.llm = llm
        self.builder = StateGraph(WorkflowState)
        self.memory = MemorySaver()

    def build_graph(self):
        """
        Configure the complete database migration workflow graph.

        Constructs a sophisticated AI-driven workflow with multiple validation loops and
        conditional routing for optimal database migration results. The graph includes
        10 specialized nodes that handle statement splitting, error identification,
        source mapping, conversion, validation, and deployment with iterative improvement.

        Workflow Architecture:
            1. Statement Splitting: Parse SQL into individual statements
            2. Error Identification: Hybrid AI/position-based error detection
            3. Error Validation: AI validates identified error statements
            4. Source Mapping: Map target errors to source statements
            5. Mapping Validation: Validate source-target mappings
            6. Statement Conversion: AI-driven SQL conversion with context
            7. Conversion Validation: Validate generated corrections
            8. Statement Replacement: Prepare corrected code for deployment
            9. Database Deployment: Deploy to target database with error capture
            10. Status Check: Determine success or iterate for improvement

        Conditional Routing:
            - Validation failures trigger feedback loops for AI improvement
            - Target-specific statements skip source mapping for efficiency
            - Deployment failures restart the workflow with updated code (max iterations configurable)
            - Successful deployment terminates the workflow
            - Maximum iteration limit reached terminates the workflow

        Returns:
            StateGraph: Configured workflow builder ready for compilation and execution
        """
        # Create fresh builder instance to avoid cached nodes and ensure clean state
        self.builder = StateGraph(WorkflowState)
        self.conversion_nodes = UniversalCodeMigrationNodes(llm=self.llm)

        # Add all workflow nodes for comprehensive database migration
        self.builder.add_node("splitStatments", self.conversion_nodes.splitStatments)
        self.builder.add_node("AnalyzeError_identifyTargetStatements", self.conversion_nodes.AnalyzeError_identifyTargetStatements)
        self.builder.add_node("validate_error_identification", self.conversion_nodes.validate_error_identification)
        self.builder.add_node("mapSource_withTargetStatements", self.conversion_nodes.mapSource_withTargetStatements)
        self.builder.add_node("validate_source_mapping", self.conversion_nodes.validate_source_mapping)
        self.builder.add_node("Convert_TargetStatement", self.conversion_nodes.Convert_TargetStatement)
        self.builder.add_node("validate_conversion", self.conversion_nodes.validate_conversion)
        self.builder.add_node("replaceTargetStatement", self.conversion_nodes.replaceTargetStatement)
        self.builder.add_node("targetcode_deployment", self.conversion_nodes.targetcode_deployment)
        self.builder.add_node("deployment_status", self.conversion_nodes.deployment_status)

        # Define linear workflow progression with validation checkpoints
        self.builder.add_edge(START, "splitStatments")
        self.builder.add_edge("splitStatments", "AnalyzeError_identifyTargetStatements")
        self.builder.add_edge("AnalyzeError_identifyTargetStatements", "validate_error_identification")

        # Error identification validation with feedback loop for AI improvement
        self.builder.add_conditional_edges(
            "validate_error_identification",
            self.should_continue_validation,
            {
                "continue": "AnalyzeError_identifyTargetStatements",  # Retry with feedback
                "proceed": "mapSource_withTargetStatements"  # Continue to mapping
            }
        )
        self.builder.add_edge("mapSource_withTargetStatements", "validate_source_mapping")

        # Source mapping validation with optimization for target-specific statements
        self.builder.add_conditional_edges(
            "validate_source_mapping",
            self.should_continue_source_mapping,
            {
                "continue": "mapSource_withTargetStatements",  # Retry mapping with feedback
                "proceed": "Convert_TargetStatement",  # Continue to conversion
                "skip_to_conversion": "Convert_TargetStatement"  # Skip for target-specific code
            }
        )
        self.builder.add_edge("Convert_TargetStatement", "validate_conversion")

        # Conversion validation with feedback loop for correction improvement
        self.builder.add_conditional_edges(
            "validate_conversion",
            self.should_continue_conversion,
            {
                "continue": "Convert_TargetStatement",  # Retry conversion with feedback
                "proceed": 'replaceTargetStatement'  # Continue to deployment preparation
            }
        )

        # Linear progression through deployment and status checking
        self.builder.add_edge("replaceTargetStatement", "targetcode_deployment")
        self.builder.add_edge("targetcode_deployment", "deployment_status")

        # Deployment status check with iteration loop for failed deployments
        self.builder.add_conditional_edges(
            "deployment_status",
            self.should_continue_or_end,
            {
                "continue": "splitStatments",  # Restart workflow with updated code
                "end": END  # Successful completion
            }
        )
        return self.builder



    def setup_graph(self):
        builder = self.build_graph()
        self.graph = builder.compile(
            interrupt_before=[], checkpointer=self.memory
        )
        return self.graph

    def invoke_graph(self, data: Dict[str, Any], thread_id: str = None) -> Dict[str, Any]:
        """
        Invoke the graph with the given input data.

        Args:
            input_data: Dictionary containing the input data for the workflow
            thread_id: Optional thread ID for the workflow execution

        Returns:
            Dictionary containing the workflow result
        """
        thread_id = thread_id or f"thread_{uuid.uuid4()}"
        thread = {"configurable": {"thread_id": thread_id}, "recursion_limit": 200}
        return self.graph.invoke(data, config=thread)

    def should_continue_validation(self, state: WorkflowState) -> str:
        """
        Determine if error identification validation should continue or proceed to next step.

        Evaluates the validation results and attempt count to decide whether to retry
        error identification or proceed to source mapping phase.

        Args:
            state: Current workflow state containing validation results

        Returns:
            "continue" to retry error identification, "proceed" to move to source mapping
        """
        validation_successful = getattr(state, 'validation_successful', False)
        validation_attempts = getattr(state, 'validation_attempts', 0)

        if validation_successful:
            print(f"✅ Validation successful after {validation_attempts} attempts - proceeding to next step")
            return "proceed"
        else:
            print(f"❌ Validation failed (attempt {validation_attempts}) - trying again")
            return "continue"

    def should_continue_source_mapping(self, state: WorkflowState) -> str:
        """Determine if we should continue source mapping validation or proceed based on validation results."""
        source_mapping_successful = getattr(state, 'source_mapping_successful', False)
        source_mapping_attempts = getattr(state, 'source_mapping_attempts', 0)

        # OPTIMIZATION: Check if error statement is target-specific
        source_context = getattr(state, 'source_context', None)
        if source_context and source_context.error_statement_number == 0:
            print("🚀 OPTIMIZATION: Error statement is target-specific (target database-only)")
            print("🎯 Skipping source mapping validation - going directly to target conversion")
            print("💡 Reason: Target-specific statements don't need source equivalence validation")
            return "skip_to_conversion"

        if source_mapping_successful:
            print(f"✅ Source mapping validation successful after {source_mapping_attempts} attempts - proceeding to next step")
            return "proceed"
        else:
            print(f"❌ Source mapping validation failed (attempt {source_mapping_attempts}) - trying again")
            return "continue"

    def should_continue_conversion(self, state: WorkflowState) -> str:
        """Determine if we should continue conversion validation or proceed based on validation results."""
        conversion_successful = getattr(state, 'conversion_successful', False)
        conversion_attempts = getattr(state, 'conversion_attempts', 0)

        if conversion_successful:
            print(f"✅ Conversion validation successful after {conversion_attempts} attempts - proceeding to next step")
            return "proceed"
        else:
            print(f"❌ Conversion validation failed (attempt {conversion_attempts}) - trying again")
            return "continue"

    def should_continue_or_end(self, state: WorkflowState) -> str:
        """
        Determine if we should continue the workflow or end based on deployment status and iteration limit.

        Decision Logic:
            - If deployment successful: End workflow
            - If max iterations reached: End workflow
            - If deployment failed and iterations < max: Continue workflow
        """
        deployment_successful = getattr(state, 'deployment_successful', False)
        max_iterations_reached = getattr(state, 'max_iterations_reached', False)
        error_message = getattr(state, 'error_message', None)
        updated_target_code = getattr(state, 'updated_target_code', None)
        current_global_iteration = getattr(state, 'current_global_iteration', 1)

        if deployment_successful:
            print("✅ Deployment successful - ending workflow")
            return "end"
        elif max_iterations_reached:
            print("🛑 Maximum iterations reached - ending workflow")
            print(f"📊 Final iteration count: {current_global_iteration}")
            print(f"🔚 Workflow terminated due to iteration limit")
            return "end"
        else:
            print(f"❌ Deployment failed - going back to split statements (iteration {current_global_iteration})")
            print(f"🔄 New error message: {error_message}")

            if updated_target_code:
                print(f"🔄 Using updated target code for next iteration")
            else:
                print(f"⚠️ No updated target code found")

            return "continue"

    def save_graph_image(self, graph):
        try:
            # Generate the PNG image
            img_data = graph.get_graph().draw_mermaid_png(
                draw_method=MermaidDrawMethod.API
                )

            # # Generate the PNG image using Pyppeteer (local browser rendering)
            # img_data = graph.get_graph().draw_mermaid_png(
            #     draw_method=MermaidDrawMethod.PYPPETEER,
            #     max_retries=5,
            #     retry_delay=2.0
            # )

            # Save the image to a file
            graph_path = "workflow_graph.png"
            with open(graph_path, "wb") as f:
                f.write(img_data)

            print(f"Graph image saved to {graph_path}")
        except Exception as e:
            print(f"Warning: Could not generate graph image: {str(e)}")
            print("Continuing execution without graph visualization...")