"""
Prompts for syntax validation in database conversion.
"""
from typing import Dict
from utils.database_names import get_database_specific_terms

def create_generic_syntax_validation_prompt(source_context: Dict, target_error_context: Dict) -> str:
    """Create a generic AI prompt for syntax validation that works with any database."""
    # Get dynamic database names
    db_terms = get_database_specific_terms()
    source_db = db_terms['source_db']
    target_db = db_terms['target_db']
    expert_title = db_terms['expert_title']

    return f"""You are a {expert_title} with deep expertise in both {source_db} and {target_db} database systems. Your task is to validate if the source {source_db} and target {target_db} SQL statements are syntactically equivalent across these database systems.

SOURCE STATEMENT:
{source_context.error_statement}

TARGET STATEMENT:
{target_error_context.error_statement}

TASK:
Analyze whether these two SQL statements are syntactically equivalent, considering they may be from different database systems (e.g., Oracle, PostgreSQL, MySQL, SQL Server, etc.).

ANALYSIS CRITERIA:

1. **FUNCTIONAL EQUIVALENCE**
   - Do both statements perform the same database operation for ANY operation type including:
     * Data Manipulation: SELECT, INSERT, UPDATE, DELETE, MERGE, UPSERT operations
     * Data Definition: CREATE, ALTER, DROP, TRUNCATE, RENAME operations
     * Transaction Control: COMMIT, ROLLBACK, SAVEPOINT, BEGIN operations
     * Procedural Operations: DECLARE, SET, CALL, EXECUTE, RETURN operations
     * Control Flow: IF/ELSE, CASE/WHEN, LOOP, WHILE, FOR, EXCEPTION handling
     * Cursor Operations: OPEN, FETCH, CLOSE cursor operations
     * Aggregate Operations: GROUP BY, HAVING, window functions, analytical functions
     * Join Operations: INNER/OUTER/CROSS joins, subqueries, CTEs
     * Index Operations: CREATE/DROP INDEX, hints, optimization directives
     * Security Operations: GRANT, REVOKE, user/role management
     * System Operations: sequence generation, trigger definitions, view creation
   - Do they achieve the same business logic and functional outcome?
   - Are the data transformations and processing patterns equivalent?

2. **STRUCTURAL ANALYSIS**
   - Compare table/view/object references (accounting for schema differences)
   - Compare column/field operations and data manipulation
   - Compare WHERE conditions, HAVING clauses, and JOIN logic
   - Compare function calls and their purposes across database dialects
   - Compare procedural logic and control flow structures
   - Compare exception handling and error management approaches

3. **SYNTAX TRANSLATION QUALITY**
   - Are database-specific functions properly translated (e.g., SYSDATE → current_timestamp)?
   - Are data types handled correctly (NUMBER → NUMERIC, VARCHAR2 → VARCHAR)?
   - Are string functions properly converted (SUBSTR → SUBSTRING, CONCAT → ||)?
   - Are mathematical and statistical functions appropriately translated?
   - Are XML/JSON processing functions correctly converted?
   - Are sequence generation methods properly handled (NEXTVAL → nextval())?
   - Are cursor operations and lifecycle management correctly translated?
   - Are transaction control statements appropriately converted?
   - Are syntax structures and procedural elements appropriately converted?
   - Are there any obvious translation errors or incompatibilities?

4. **SEMANTIC PRESERVATION**
   - Is the meaning and intent of the original statement preserved?
   - Are edge cases and error conditions handled consistently?
   - Are NULL handling and data type conversions appropriate?
   - Is business logic and functional behavior maintained?
   - Are performance characteristics and optimization hints preserved where possible?

IMPORTANT GUIDELINES:
- DO NOT assume specific database types (Oracle, PostgreSQL, etc.)
- Focus on logical and functional equivalence
- Consider that different databases have different syntax for the same operations
- Evaluate translation quality without hardcoded keyword matching
- Look for semantic equivalence rather than exact syntax matching

OUTPUT FORMAT (JSON):
{{
  "is_syntactically_equivalent": true/false,
  "equivalence_score": <float between 0.0 and 1.0>,
  "translation_quality": "excellent/good/fair/poor",
  "explanation": "<concise analysis summary>",
  "syntax_differences": [
    "<list of syntax differences identified>"
  ],
  "functional_assessment": {{
    "same_operation_type": true/false,
    "same_business_logic": true/false,
    "proper_data_handling": true/false
  }},
  "recommendations": [
    "<suggestions for improvement if equivalence is low>"
  ]
}}

Provide clear, concise reasoning for your assessment."""
