# QMigrator AI - Oracle to PostgreSQL Migration System
## Developer Guide

### Table of Contents
1. [System Overview](#system-overview)
2. [Architecture & Components](#architecture--components)
3. [Workflow Analysis](#workflow-analysis)
4. [Code Structure & Organization](#code-structure--organization)
5. [AI Integration & Pydantic Models](#ai-integration--pydantic-models)
6. [Streamlit Web Interface](#streamlit-web-interface)
7. [Development Guidelines](#development-guidelines)
8. [Installation & Setup](#installation--setup)

---

## System Overview

QMigrator AI is an advanced AI-powered workflow system designed to automate database migration through intelligent error analysis, statement mapping, and iterative correction. The system leverages multiple LLM providers to understand deployment errors, map source database code to target database code, and generate corrected statements until successful deployment.

### Key Features
- **AI-Driven Analysis**: Uses multiple LLM providers (OpenAI, Azure OpenAI, Anthropic, Groq, Gemini, Ollama) for SQL understanding
- **Hybrid Error Detection**: Combines position-based resolution with AI validation for optimal accuracy
- **Iterative Improvement**: Automatically loops until successful PostgreSQL deployment with configurable iteration limits
- **Real Database Testing**: Deploys to actual PostgreSQL database for validation
- **Comprehensive Audit Trail**: Maintains detailed history with Excel and SQL file outputs for each iteration
- **Multi-Layer Validation**: AI validates each critical step with feedback integration and confidence scoring
- **Interactive Web Interface**: Modern Streamlit-based UI with real-time progress tracking
- **Position-Based Error Resolution**: Advanced error position mapping for precise statement identification
- **Validation Feedback Loops**: AI learns from validation failures to improve subsequent attempts

### Business Value
- **Reduces Migration Time**: Automates complex Oracle to PostgreSQL conversions with minimal manual intervention
- **Improves Accuracy**: AI-driven analysis reduces human error in SQL conversion with confidence scoring
- **Provides Transparency**: Complete audit trail for compliance and debugging with detailed change tracking
- **Scalable Solution**: Handles complex migration scenarios with iterative improvement capabilities

---

## Architecture & Components

### Core Technology Stack
- **LangGraph**: Advanced workflow orchestration with state management, conditional routing, and memory checkpointing
- **Pydantic**: Type-safe data models for AI structured outputs with comprehensive validation
- **Multiple LLM Providers**: OpenAI, Azure OpenAI, Anthropic, Groq, Gemini, Ollama with unified interface
- **PostgreSQL**: Real database deployment and validation using psycopg2
- **Streamlit**: Modern web-based user interface with real-time progress tracking
- **Python 3.12+**: Core implementation with comprehensive error handling and type hints
- **Pandas & OpenPyXL**: Excel file generation for audit trails and data analysis

### System Components

#### 1. Configuration Management (`config/`)
- **ConfigManager**: Centralized Pydantic-based configuration with environment variable support
- **ModelConfig**: Provider-specific LLM configurations with validation and defaults
- **Environment Integration**: Secure credential management through environment variables
- **Multi-Provider Support**: Unified configuration interface for all LLM providers

#### 2. Language Model Integration (`llm/`)
- **Multi-Provider Support**: Flexible AI provider integration with consistent interface
- **Structured Outputs**: Pydantic model integration for reliable AI responses
- **Error Handling**: Robust error handling for AI provider failures with fallback strategies
- **Confidence Scoring**: AI confidence assessment for quality control

#### 3. State Management (`state/`)
- **WorkflowState**: Comprehensive Pydantic model for complete workflow state tracking
- **AI Response Models**: Structured models for all AI outputs with validation
- **Type Safety**: Full type checking for reliable data flow throughout the system
- **Validation Feedback**: Feedback loop integration for AI learning and improvement

#### 4. Workflow Orchestration (`workflow/`)
- **GraphBuilder**: Advanced LangGraph workflow construction with conditional routing
- **Conditional Logic**: Intelligent routing based on validation results and optimization rules
- **Memory Management**: Workflow state persistence and checkpointing
- **Iteration Control**: Configurable iteration limits and termination conditions

#### 5. Migration Nodes (`nodes/`)
- **UniversalCodeMigrationNodes**: Complete suite of migration operations with AI integration
- **Validation Loops**: Multi-layer validation with feedback integration at each step
- **Position-Based Resolution**: Advanced error position mapping for precise identification
- **AI Corrections**: Structured AI-generated corrections with detailed change tracking

#### 6. Utilities (`utils/`)
- **Error Position Mapping**: Precise error position mapping for statement identification
- **SQL Formatting**: Advanced statement splitting and formatting utilities
- **File Management**: Output file generation and organization for audit trails

#### 7. Prompts (`prompts/`)
- **Specialized Prompts**: AI prompts optimized for each workflow step and scenario
- **Context-Aware**: Prompts adapted for different migration scenarios and error types
- **Validation Focused**: Prompts designed for reliable AI validation with confidence scoring

#### 8. User Interface (`streamlit_main.py`)
- **Modern Web Interface**: Streamlit-based interactive migration interface with custom styling
- **Real-time Progress**: Visual workflow progress tracking with step-by-step indicators
- **Input Management**: Comprehensive code input and error message handling
- **Session Management**: Persistent workflow state across user sessions
- **Responsive Design**: Mobile-friendly interface with optimized layouts

---

## Workflow Analysis

### Workflow Architecture
The QMigrator AI workflow consists of 10 specialized nodes that handle the complete Oracle to PostgreSQL migration process:

```
START → Split Statements → Identify Error → Validate Error → Map Source →
Validate Mapping → Convert Statements → Validate Conversion → Replace Statements →
Deploy Code → Check Status → (END or ITERATE)
```

### Implemented Workflow Nodes

#### 1. Split Statements (`splitStatments`)
**Purpose**: Parse SQL code into individual statements for granular analysis

**Implementation**:
- Splits source database code and target database code into numbered statements
- Creates position mapping for error location
- Optimizes for loop iterations by reusing source statements
- Generates Excel files for audit trail

**Key Features**:
- SQL statement parsing with comment and quote handling
- Position tracking for error resolution
- Iteration-based file naming

#### 2. Analyze Error & Identify Target Statements (`AnalyzeError_identifyTargetStatements`)
**Purpose**: Identify which specific target statement is causing the deployment error

**Hybrid Approach**:
1. **Position-Based Resolution**: Uses error line/position information from PostgreSQL errors
2. **AI Validation**: Validates if position-identified statement causes the exact error
3. **AI Fallback**: Two-phase AI analysis if position-based approach fails

**Implementation**:
- Extracts line/position from deployment error messages
- Maps error position to specific target statement
- Creates error context (before/error/after statements)
- Uses AI for validation and fallback analysis

#### 3. Validate Error Identification (`validate_error_identification`)
**Purpose**: AI validates that the correct error statement was identified

**Implementation**:
- Uses AI with confidence scoring for validation
- Generates feedback for failed validations
- Tracks validation attempts for quality control
- Incorporates feedback into next identification attempt

#### 4. Map Source with Target Statements (`mapSource_withTargetStatements`)
**Purpose**: Maps PostgreSQL error statements back to corresponding Oracle source statements

**Implementation**:
- AI identifies Oracle statements achieving same business outcome
- Creates sequential mapping around error statement
- Handles database-specific statements by mapping to 0 (no source equivalent)
- Focuses on functional equivalence over syntax similarity

#### 5. Validate Source Mapping (`validate_source_mapping`)
**Purpose**: AI validates the accuracy of source-to-target statement mapping

**Implementation**:
- Validates functional equivalence between source and target
- Confirms database-specific statements are correctly unmapped
- Generates feedback for mapping improvement
- Optimizes by skipping validation for PostgreSQL-specific code

#### 6. Convert Target Statement (`Convert_TargetStatement`)
**Purpose**: AI converts problematic PostgreSQL statements using dynamic strategy

**Implementation**:
- Uses source database context for business logic understanding
- Applies target database expertise for database-specific statements
- Structured outputs with detailed change tracking
- Incorporates feedback from previous validation failures

#### 7. Validate Conversion (`validate_conversion`)
**Purpose**: AI validates that generated corrections are accurate

**Implementation**:
- Validates functional equivalence with source database
- Checks appropriate target database syntax and features
- Ensures corrections address deployment errors
- Generates feedback for failed validations

#### 8. Replace Target Statement (`replaceTargetStatement`)
**Purpose**: Prepares AI corrections for deployment phase

**Implementation**:
- Passes AI corrections directly to deployment
- Maintains state consistency for deployment processing
- Preserves iteration tracking for audit trail

#### 9. Deploy Target Code (`targetcode_deployment`)
**Purpose**: Applies corrections and deploys to PostgreSQL database

**Implementation**:
- Applies AI corrections to create updated code
- Deploys to PostgreSQL with error capture
- Uses psycopg2 for PostgreSQL connections
- Captures detailed error messages for next iteration

#### 10. Check Deployment Status (`deployment_status`)
**Purpose**: Determines workflow continuation based on deployment results

**Implementation**:
- Checks deployment success/failure status
- Ends workflow on successful deployment
- Prepares next iteration on deployment failure
- Tracks iterations across workflow runs

---

## Code Structure & Organization

### Directory Structure
```
QM_Conversion_Agent1/
├── config/                 # Configuration management
│   ├── __init__.py
│   ├── config_manager.py   # Centralized Pydantic-based configuration
│   └── model_config.py     # LLM provider configurations
├── llm/                   # Language model integrations
│   ├── __init__.py
│   ├── azure_openai_llm.py # Azure OpenAI integration
│   ├── openai_llm.py      # OpenAI integration
│   ├── anthropic_llm.py   # Anthropic Claude integration
│   ├── groq_llm.py        # Groq integration
│   ├── gemini_llm.py      # Google Gemini integration
│   └── ollama_llm.py      # Ollama local models integration
├── state/                 # Pydantic state models
│   ├── __init__.py
│   └── state.py           # Complete workflow state and AI response models
├── workflow/              # LangGraph workflow definition
│   ├── __init__.py
│   └── graph_builder.py   # Advanced workflow graph construction
├── nodes/                 # Migration workflow nodes
│   ├── __init__.py
│   └── conversion_nodes.py # Complete migration node implementations
├── utils/                 # Utility functions
│   ├── __init__.py
│   ├── error_position_mapper.py # Position-based error resolution
│   └── sql_formatter.py   # SQL statement processing utilities
├── prompts/               # AI prompts for each workflow step
│   ├── __init__.py
│   ├── error_identification_prompts.py
│   ├── mapping_prompts.py
│   ├── conversion_prompts.py
│   └── validation_prompts.py
├── formatting/            # SQL formatting utilities
│   ├── __init__.py
│   └── sql_statement_splitter.py
├── metadata/              # Database connection and metadata
│   ├── __init__.py
│   └── db_connection.py   # Target database connection management
├── output_files/          # Generated workflow artifacts
│   ├── iteration_1/       # First iteration outputs
│   ├── iteration_2/       # Second iteration outputs
│   └── ...               # Additional iterations
├── pyproject.toml         # Project dependencies and configuration
├── main.py               # Command-line application entry point
├── streamlit_main.py     # Modern web interface with progress tracking
└── workflow_graph.png    # Generated workflow visualization
```

### Key Design Principles

#### 1. Pydantic-First Approach
- All data models use Pydantic for type safety
- Structured AI outputs ensure reliable parsing
- Comprehensive validation at data boundaries

#### 2. Function-Based Utilities
- Utility functions are normal functions, not static methods
- Clear separation of concerns
- Easy testing and maintenance

#### 3. Comprehensive Documentation
- Every function and class has detailed docstrings
- Clear parameter and return type documentation
- Usage examples where appropriate

#### 4. Error Handling
- Graceful error handling throughout the system
- Detailed error messages for debugging
- Fallback strategies for AI failures

---

## AI Integration & Pydantic Models

### Structured AI Outputs
The system uses comprehensive Pydantic models to ensure reliable AI responses across all workflow operations with type safety and validation.

### Key Pydantic Models
- **WorkflowState**: Comprehensive main state management with 30+ fields for complete workflow tracking
- **CorrectedStatement**: AI-generated statement corrections with detailed change tracking
- **ValidationOutput**: AI validation results with confidence scoring and detailed explanations
- **Phase1ErrorIdentificationOutput**: Error statement identification results with reasoning
- **Phase2ErrorContextOutput**: Error context creation results with validation notes
- **StatementConversionOutput**: Complete conversion results with structured corrections
- **ErrorContext**: Context around error statements with before/error/after structure
- **StatementMapping**: Source-to-target statement mappings with status tracking

### AI Provider Integration
- **Multi-Provider Support**: OpenAI, Azure OpenAI, Anthropic, Groq, Gemini, Ollama with unified interface
- **Consistent Interface**: Standardized LLM integration across all providers
- **Structured Outputs**: Pydantic model integration for reliable AI responses
- **Error Handling**: Robust error handling with fallback strategies for provider failures
- **Configuration Management**: Centralized configuration with environment variable support

### Confidence Scoring & Quality Control
- **Universal Confidence Scoring**: All AI operations include confidence scores (0.0-1.0)
- **Quality Assessment**: Enables automated quality assessment and decision making
- **Iterative Improvement**: Supports iterative improvement strategies based on confidence levels
- **Validation Thresholds**: Configurable confidence thresholds for validation acceptance
- **Feedback Integration**: AI learns from validation failures to improve subsequent attempts

---

## Streamlit Web Interface

### Overview
The QMigrator AI features a modern, interactive web interface built with Streamlit that provides real-time progress tracking, intuitive input management, and comprehensive workflow visualization.

### Key Features

#### 1. Real-Time Progress Tracking
- **Visual Progress Indicators**: Step-by-step workflow progress with color-coded status indicators
- **Current Node Highlighting**: Dynamic highlighting of the currently executing workflow node
- **Progress Percentage**: Real-time calculation and display of overall workflow completion
- **Iteration Tracking**: Clear indication of current iteration number and step count

#### 2. Interactive Input Management
- **Tabbed Interface**: Organized input sections for Oracle source code, PostgreSQL target code, and deployment errors
- **Code Editor Styling**: Custom black background with white text for optimal code readability
- **Syntax Highlighting**: Monospace font with proper formatting for SQL code
- **Persistent State**: Session state management to maintain inputs across workflow runs

#### 3. Workflow Visualization
- **Dynamic Graph Display**: Automatically generated workflow diagram showing node relationships
- **Status Indicators**: Visual representation of completed, active, pending, and error states
- **Animation Effects**: Smooth transitions and pulse animations for active nodes
- **Responsive Design**: Mobile-friendly interface with optimized layouts

#### 4. User Experience Features
- **Custom CSS Styling**: Professional appearance with gradient headers and modern design
- **Error Handling**: Graceful error display with helpful error messages
- **Reset Functionality**: One-click reset to clear all inputs and restart workflow
- **Results Summary**: Comprehensive results display with key metrics and file locations

### Technical Implementation

#### Session State Management
The interface maintains workflow state across user sessions with persistent tracking of progress and inputs.

#### Progress Tracking System
Real-time workflow tracking with step identification and user-friendly progress indicators.

#### Custom Styling
- **Dark Code Editor**: Black background with white text for optimal code visibility
- **Progress Animations**: CSS animations for active workflow steps
- **Responsive Layout**: Flexible design that adapts to different screen sizes
- **Professional Appearance**: Gradient headers and modern UI components

### Usage Instructions

1. **Start the Application**: Run the Streamlit interface
2. **Input Your Data**: Provide source database code, target database code, and deployment error
3. **Run Migration**: Start the workflow and monitor real-time progress
4. **Review Results**: Check migration results and access generated files

### Configuration
The Streamlit interface automatically detects and uses the configured LLM provider from environment variables, supporting all available providers (OpenAI, Azure OpenAI, Anthropic, Groq, Gemini, Ollama).

---

## Development Guidelines

### Code Quality Standards
1. **Type Hints**: Use comprehensive type hints throughout
2. **Documentation**: Maintain detailed docstrings for all functions and classes
3. **Error Handling**: Implement robust error handling with graceful degradation
4. **Pydantic Models**: Use Pydantic for all data structures and AI outputs

### File Organization
1. **Pydantic Models**: Keep all models in `state/state.py`
2. **Prompts**: Store AI prompts in `prompts/` folder
3. **Utilities**: Use normal functions instead of static methods
4. **Documentation**: Maintain comprehensive docstrings and comments

### Adding New Features
1. **Define Pydantic Models**: Create structured models for new data types
2. **Update State**: Extend WorkflowState for new state requirements
3. **Create Prompts**: Develop specialized prompts for new AI operations
4. **Add Validation**: Implement validation for new workflow steps
5. **Update Documentation**: Maintain comprehensive documentation

### Best Practices
1. **Source Statement Reuse**: Optimize for iteration loops
2. **Position Mapping**: Use position-based resolution when possible
3. **Audit Trail**: Maintain complete file-based audit trail with Excel outputs
4. **Iteration Tracking**: Track workflow iterations for analysis
5. **Error Analysis**: Analyze deployment errors for patterns

---

## Installation & Setup

### Prerequisites
- **Python 3.12+**: Required for modern type hints and performance optimizations
- **PostgreSQL Database**: For deployment testing and validation
- **Git**: For version control and repository management

### Installation Steps

#### 1. Clone the Repository
Clone the project repository and navigate to the QM_Conversion_Agent1 directory.

#### 2. Create Virtual Environment
Set up a Python virtual environment and activate it.

#### 3. Install Dependencies
Install all required dependencies using pip.

#### 4. Environment Configuration
Create a `.env` file with your LLM provider credentials and PostgreSQL database configuration. Configure the LLM provider (azure_openai, openai, anthropic, groq, gemini, ollama) and corresponding API keys.

#### 5. Database Setup
Ensure PostgreSQL is running and accessible with the configured credentials. The system will use this database for deployment testing.

### Running the Application

#### Command Line Interface
Run the main Python application for command-line usage.

#### Web Interface
Launch the Streamlit web interface for interactive migration with real-time progress tracking.

### Project Dependencies

The system uses the following key dependencies (defined in `pyproject.toml`):

- **LangGraph 0.3.30+**: Workflow orchestration and state management
- **Pydantic 2.11.3+**: Type-safe data models and validation
- **Streamlit 1.45.1+**: Modern web interface framework
- **psycopg2 2.9.10+**: PostgreSQL database connectivity
- **OpenPyXL 3.1.5+**: Excel file generation for audit trails
- **Pandas 2.2.3+**: Data manipulation and analysis
- **LangChain Providers**: Multiple LLM provider integrations

### Troubleshooting

#### Common Issues

1. **Import Errors**: Ensure all dependencies are installed and virtual environment is activated
2. **LLM Provider Errors**: Verify API keys and endpoint configurations in `.env` file
3. **Database Connection**: Check PostgreSQL service status and connection parameters
4. **Streamlit Issues**: Clear browser cache and restart the Streamlit server

#### Debug Mode
Enable debug logging by setting the LOG_LEVEL environment variable to DEBUG.

### Development Setup
For development work, install development dependencies, use code formatting tools (black, isort), enable type checking (mypy), and run tests (pytest).

This guide provides the foundation for understanding and developing with the QMigrator AI system. For specific implementation details, refer to the individual code files and their comprehensive documentation.
