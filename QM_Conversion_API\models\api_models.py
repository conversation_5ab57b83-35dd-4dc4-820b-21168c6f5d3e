from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime


class MigrationRequest(BaseModel):
    """
    Request model for starting a database migration workflow.
    
    This model defines the input parameters required to initiate an AI-powered
    database migration process. All fields are required for the workflow to
    function properly.
    
    Attributes:
        source_code: Original source database code to be migrated
        target_code: Target database code with potential deployment errors
        deployment_error: Error message from target database deployment attempt
    """
    source_code: str = Field(
        ...,
        description="Original source database code (e.g., Oracle stored procedure)",
        example="""CREATE OR REPLACE PROCEDURE sample_proc AS
BEGIN
    SELECT * FROM users;
END;"""
    )
    target_code: str = Field(
        ...,
        description="Target database code with deployment errors (e.g., PostgreSQL)",
        example="""CREATE OR REPLACE FUNCTION sample_proc() RETURNS void AS $$
BEGIN
    SELECT * FROM users;
END;
$$ LANGUAGE plpgsql;"""
    )
    deployment_error: str = Field(
        ...,
        description="Error message from target database deployment attempt",
        example="ERROR: syntax error at or near 'PROCEDURE' at line 1"
    )


class MigrationResponse(BaseModel):
    """
    Response model for migration workflow initiation.
    
    Returned when a migration workflow is successfully started. Contains
    the job identifier needed to track progress and retrieve results.
    
    Attributes:
        job_id: Unique identifier for tracking the migration workflow
        status: Current status of the migration (typically 'started')
        message: Human-readable status message
    """
    job_id: str = Field(
        ...,
        description="Unique identifier for tracking the migration workflow"
    )
    status: str = Field(
        ...,
        description="Current status of the migration",
        example="started"
    )
    message: str = Field(
        ...,
        description="Human-readable status message",
        example="Migration workflow initiated successfully"
    )


class MigrationStatus(BaseModel):
    """
    Model for migration workflow status information.
    
    Provides real-time status updates for ongoing migration workflows,
    including progress percentage and current workflow step.
    
    Attributes:
        job_id: Unique identifier of the migration job
        status: Current status (started, running, completed, failed)
        message: Human-readable status message
        progress: Progress percentage (0-100)
        current_step: Current workflow step being executed
        estimated_time_remaining: Estimated time to completion in seconds
    """
    job_id: str = Field(
        ...,
        description="Unique identifier of the migration job"
    )
    status: str = Field(
        ...,
        description="Current status of the migration",
        example="running"
    )
    message: str = Field(
        ...,
        description="Human-readable status message",
        example="Converting target statements using AI"
    )
    progress: int = Field(
        ...,
        description="Progress percentage (0-100)",
        ge=0,
        le=100,
        example=65
    )
    current_step: str = Field(
        ...,
        description="Current workflow step being executed",
        example="Convert_TargetStatement"
    )
    estimated_time_remaining: Optional[float] = Field(
        None,
        description="Estimated time to completion in seconds",
        example=120.5
    )


class MigrationResult(BaseModel):
    """
    Model for completed migration workflow results.
    
    Contains the complete results of a finished migration workflow,
    including success status, final code, and audit information.
    
    Attributes:
        job_id: Unique identifier of the migration job
        success: Whether the migration completed successfully
        deployment_successful: Whether the final deployment was successful
        iteration_count: Number of iterations required for completion
        updated_target_code: Final corrected target database code
        error_message: Error message if migration failed
        final_state: Complete final state of the workflow
    """
    job_id: str = Field(
        ...,
        description="Unique identifier of the migration job"
    )
    success: bool = Field(
        ...,
        description="Whether the migration completed successfully"
    )
    deployment_successful: Optional[bool] = Field(
        None,
        description="Whether the final deployment was successful"
    )
    iteration_count: Optional[int] = Field(
        None,
        description="Number of iterations required for completion",
        example=3
    )
    updated_target_code: Optional[str] = Field(
        None,
        description="Final corrected target database code"
    )
    error_message: Optional[str] = Field(
        None,
        description="Error message if migration failed"
    )
    final_state: Optional[Dict[str, Any]] = Field(
        None,
        description="Complete final state of the workflow for audit purposes"
    )


class HealthResponse(BaseModel):
    """
    Model for health check responses.
    
    Simple model for API health status information.
    
    Attributes:
        status: Health status of the API
        service: Name of the service
        version: Version of the API
        timestamp: Current timestamp
    """
    status: str = Field(
        ...,
        description="Health status of the API",
        example="healthy"
    )
    service: str = Field(
        ...,
        description="Name of the service",
        example="QMigrator AI FastAPI"
    )
    version: str = Field(
        ...,
        description="Version of the API",
        example="1.0.0"
    )
    timestamp: Optional[datetime] = Field(
        None,
        description="Current timestamp"
    )


class JobSummary(BaseModel):
    """
    Model for job summary information in job listings.
    
    Lightweight model for displaying job information in lists.
    
    Attributes:
        job_id: Unique identifier of the migration job
        status: Current status of the migration
        progress: Progress percentage (0-100)
        current_step: Current workflow step
        created_at: Job creation timestamp
    """
    job_id: str = Field(
        ...,
        description="Unique identifier of the migration job"
    )
    status: str = Field(
        ...,
        description="Current status of the migration"
    )
    progress: int = Field(
        ...,
        description="Progress percentage (0-100)",
        ge=0,
        le=100
    )
    current_step: str = Field(
        ...,
        description="Current workflow step"
    )
    created_at: float = Field(
        ...,
        description="Job creation timestamp"
    )


class JobListResponse(BaseModel):
    """
    Model for job list responses.
    
    Contains a list of all migration jobs with summary information.
    
    Attributes:
        jobs: List of job summaries
        total_count: Total number of jobs
    """
    jobs: List[JobSummary] = Field(
        ...,
        description="List of job summaries"
    )
    total_count: int = Field(
        ...,
        description="Total number of jobs",
        example=5
    )
