import sys
import uuid
from typing import Dict, Any
from config import Config<PERSON>ana<PERSON>
from llm import (
    AzureOpenAILLM,
    OpenAILLM,
    AnthropicLLM,
    GroqLLM,
    GeminiLLM,
    OllamaLLM
)
from workflow import GraphBuilder
from dotenv import load_dotenv
load_dotenv()


def create_llm(provider: str, config_manager: ConfigManager) -> Any:
    """
    Create and initialize a Language Model instance based on the specified provider.

    This function creates LLM instances for Oracle to PostgreSQL database migration workflows.
    It supports multiple AI providers to ensure flexibility and reliability in the conversion process.
    Each provider is configured with appropriate settings for optimal SQL conversion performance.

    Supported Providers:
        - azure_openai: Microsoft Azure OpenAI service with enterprise-grade security
        - openai: OpenAI's GPT models for advanced SQL understanding
        - anthropic: Anthropic's Claude models for precise code analysis
        - groq: Groq's high-performance inference for fast processing
        - gemini: Google's Gemini models for comprehensive language understanding
        - ollama: Local LLM deployment for privacy-sensitive environments

    Args:
        provider (str): The LLM provider identifier from supported list
        config_manager (ConfigManager): Configuration manager with provider-specific settings

    Returns:
        Any: Initialized LLM instance ready for database migration tasks

    Raises:
        ValueError: If the provider is not supported or configuration is invalid

    Example:
        >>> config = ConfigManager()
        >>> llm = create_llm("azure_openai", config)
        >>> # LLM ready for SQL conversion tasks
    """
    if provider == "azure_openai":
        return AzureOpenAILLM(config_manager)
    elif provider == "openai":
        return OpenAILLM(config_manager)
    elif provider == "anthropic":
        return AnthropicLLM(config_manager)
    elif provider == "groq":
        return GroqLLM(config_manager)
    elif provider == "gemini":
        return GeminiLLM(config_manager)
    elif provider == "ollama":
        return OllamaLLM(config_manager)
    else:
        raise ValueError(f"Unsupported LLM provider: {provider}")


def setup_application() -> Any:
    """
    Set up the QMigrator AI application with configuration and initialize the Language Model.

    This function handles the complete application initialization process for Oracle to PostgreSQL
    database migration workflows. It loads configuration settings from environment variables,
    validates the LLM provider selection, and initializes the appropriate AI client for
    SQL conversion tasks.

    The setup process includes:
        - Loading configuration from environment variables
        - Validating LLM provider availability
        - Initializing the selected AI provider with proper credentials
        - Preparing the LLM for database migration operations

    Returns:
        Any: Initialized LLM instance ready for Oracle to PostgreSQL migration workflows

    Raises:
        Exception: If LLM initialization fails due to invalid credentials, network issues,
                  or unsupported provider configuration

    Example:
        >>> llm = setup_application()
        >>> # LLM is now ready for database migration tasks
    """
    config_manager = ConfigManager()
    llm_provider = config_manager.get_llm_provider()

    try:
        print(f"🔧 Attempting to initialize {llm_provider} LLM for database migration...")
        llm = create_llm(llm_provider, config_manager)
        print(f"✅ Successfully initialized {llm_provider} client for SQL conversion")
        return llm
    except Exception as e:
        print(f"❌ Error initializing {llm_provider}: {str(e)}")
        raise


def run_workflow(llm: Any, source_code: str, target_code: str, deployment_error: str) -> Dict[str, Any]:
    """
    Execute the complete Oracle to PostgreSQL database migration workflow.

    This function orchestrates the entire AI-driven migration process using a sophisticated
    workflow that includes error analysis, source mapping, statement conversion, and validation.
    The workflow uses LangGraph for state management and supports iterative improvements
    until successful PostgreSQL deployment.

    Workflow Steps:
        1. Split SQL statements for granular analysis
        2. Identify problematic target statements using hybrid AI/position-based approach
        3. Map target statements to corresponding Oracle source statements
        4. Convert statements using AI with source context
        5. Validate conversions and deploy to PostgreSQL
        6. Iterate until successful deployment

    Args:
        llm (Any): Initialized Language Model instance for AI-driven SQL analysis
        source_code (str): Original Oracle database code to be migrated
        target_code (str): PostgreSQL target code with potential deployment errors
        deployment_error (str): Error message from PostgreSQL deployment attempt

    Returns:
        Dict[str, Any]: Workflow execution results containing final state, iteration count,
                       deployment status, and complete audit trail

    Example:
        >>> llm = setup_application()
        >>> result = run_workflow(llm, oracle_sql, postgres_sql, error_msg)
        >>> print(f"Migration completed in {result['iteration_count']} iterations")
    """
    # Initialize the workflow graph builder with the LLM
    graph_builder = GraphBuilder(llm)
    graph_builder.setup_graph()

    # Generate workflow visualization for debugging and documentation
    graph_builder.save_graph_image(graph_builder.graph)

    # Create a unique thread ID for this workflow execution to enable state tracking
    thread_id = f"thread_{uuid.uuid4()}"
    print(f"🔗 Using thread ID: {thread_id}")

    # Execute the complete migration workflow with initial state
    result = graph_builder.invoke_graph({
        "source_code": source_code,
        "target_code": target_code,
        "deployment_error": deployment_error,
        "iteration_count": 1  # Initialize iteration count for tracking
    }, thread_id=thread_id)
    return result


def main():
    """
    Main entry point for the QMigrator AI Oracle to PostgreSQL migration system.

    This function demonstrates the complete migration workflow using sample Oracle and PostgreSQL
    code with a deployment error. In production environments, these inputs would be provided
    through the Streamlit web interface or command-line arguments.

    The main function:
        - Sets up the AI application with proper LLM configuration
        - Executes the complete migration workflow
        - Handles errors gracefully with detailed error reporting
        - Provides clear success/failure feedback

    Sample Data:
        - Oracle stored procedure with complex business logic
        - PostgreSQL conversion with deployment errors
        - Actual PostgreSQL error message for testing

    Raises:
        ValueError: If configuration is invalid or LLM provider is unsupported
        Exception: If unexpected errors occur during workflow execution
    """
    try:
        # Sample Oracle source code for testing the migration workflow
        # In production, this would be provided by users through the web interface
        source_code = """
        
  CREATE OR REPLACE  PROCEDURE "BILLING"."P_ACKNOWLEDGECASHTOCOUNTER" 
 (
        IC_UPDATEACK  IN CLOB,
        OC_BALANCE    OUT SYS_REFCURSOR
 )
as
LV_UPDATEACK      XMLTYPE;
LV_LOCATIONID     NATURALACCOUNTMASTER.LOCATIONID%type;
LV_COMPANYID      NATURALACCOUNTMASTER.COMPANYID%type;
LV_UPDATEDBY      TRANSACTION.UPDATEDBY%type;
LV_TRANSACTIONID  TRANSACTION.TRANSACTIONID%type;
LV_TRANAMOUNT     TRANSACTION.TRANAMOUNT%type;
LV_TRANEVENT     TRANSACTION.TRANEVENT%type;
begin
LV_UPDATEACK      :=XMLTYPE(IC_UPDATEACK);
LV_TRANSACTIONID  :=TRIM(LV_UPDATEACK.EXTRACT('//CashHandlingDetails/@TransactionNo').GETSTRINGVAL());
LV_LOCATIONID     :=TRIM(LV_UPDATEACK.EXTRACT('//CashHandlingDetails/@LocationID').GETSTRINGVAL());
LV_COMPANYID      :=TRIM(LV_UPDATEACK.EXTRACT('//CashHandlingDetails/@CompanyID').GETSTRINGVAL());
LV_UPDATEDBY      :=TRIM(LV_UPDATEACK.EXTRACT('//LoginID/text()').GETSTRINGVAL());

SAVEPOINT S_SAVEPOINT;

SELECT  DISTINCT TRANEVENT INTO LV_TRANEVENT
FROM    TRANSACTION
WHERE   TRANSACTIONID=LV_TRANSACTIONID
AND     ENTRYSTATUS='ENTERED'
AND     (REVERSALFLAG=1 AND DELETEFLAG=1);



CASE

--------------------START ACKNOWLEDGE CASH TRANSFER CASH IN HAND TO COUNTER

    WHEN LV_TRANEVENT='CASHTRANSFERCASHINHANDTOCOUNTER' THEN

       DECLARE
            LV_CASHACCOUNT    VARCHAR2(20);
            LV_CASHINHAND     VARCHAR2(20);
       BEGIN
        UPDATE  TRANSACTION
                SET   ENTRYSTATUS='POSTED',
                      UPDATEDBY=LV_UPDATEDBY,
                      UPDATEDDATE=SYSDATE
                WHERE TRANSACTIONID=LV_TRANSACTIONID;

        SELECT  ACCOUNTNO,TRANAMOUNT INTO LV_CASHACCOUNT,LV_TRANAMOUNT
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='DR';

        SELECT  DISTINCT ACCOUNTNO INTO LV_CASHINHAND
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='CR';

        UPDATE  NATURALACCOUNTMASTER
         SET    CUMCREDITAMOUNT=NVL(CUMCREDITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
         WHERE  ACCOUNTID=LV_CASHINHAND
         AND    ACCOUNTTYPE='CASHINHAND'
         AND    LOCATIONID=LV_LOCATIONID
         AND    COMPANYID=LV_COMPANYID;

        UPDATE  CASHACCOUNTMASTER
           SET  CUMDEBITAMOUNT=NVL(CUMDEBITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
         WHERE  ACCOUNTID=LV_CASHACCOUNT
         AND    LOCATIONID=LV_LOCATIONID
         AND    COMPANYID=LV_COMPANYID;

          OPEN    OC_BALANCE FOR
          SELECT  ACCOUNTID,ACCOUNTNAME,(nvl(CUMDEBITAMOUNT,0)-nvl(CUMCREDITAMOUNT,0)) BALANCE
          FROM    NATURALACCOUNTMASTER
          WHERE   ACCOUNTID=LV_CASHINHAND
          AND     COMPANYID=LV_LOCATIONID
          AND     LOCATIONID=LV_LOCATIONID

          UNION
          SELECT  ACCOUNTID,ACCOUNTNAME,(nvl(CUMDEBITAMOUNT,0)-nvl(CUMCREDITAMOUNT,0)) BALANCE
          FROM    CASHACCOUNTMASTER
          WHERE   ACCOUNTID=LV_CASHACCOUNT
          AND     COMPANYID=LV_LOCATIONID
          AND     LOCATIONID=LV_LOCATIONID;
      END;
----------------------END ACKNOWLEDGE CASH TRANSFER CASH IN HAND TO COUNTER

----------------------START ACKNOWLEDGE CASH TRANSFER COUNTER TO COUNTER

 WHEN LV_TRANEVENT='CASHTRANSFERCOUNTERTOCOUNTER' THEN

    DECLARE
            LV_FROMCASHACCOUNT    VARCHAR2(20);
            LV_TOCASHACCOUNT      VARCHAR2(20);
      BEGIN
      UPDATE  TRANSACTION
                SET   ENTRYSTATUS='POSTED',
                      UPDATEDBY=LV_UPDATEDBY,
                      UPDATEDDATE=SYSDATE
                WHERE TRANSACTIONID=LV_TRANSACTIONID;

        SELECT  ACCOUNTNO,TRANAMOUNT INTO LV_FROMCASHACCOUNT,LV_TRANAMOUNT
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='CR';

        SELECT  DISTINCT ACCOUNTNO INTO LV_TOCASHACCOUNT
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='DR';

        UPDATE  CASHACCOUNTMASTER
           SET  CUMDEBITAMOUNT=NVL(CUMDEBITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
         WHERE  ACCOUNTID=LV_FROMCASHACCOUNT
         AND    LOCATIONID=LV_LOCATIONID
         AND    COMPANYID=LV_COMPANYID;

        UPDATE  CASHACCOUNTMASTER
           SET  CUMDEBITAMOUNT=NVL(CUMDEBITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
         WHERE  ACCOUNTID=LV_TOCASHACCOUNT
         AND    LOCATIONID=LV_LOCATIONID
         AND    COMPANYID=LV_COMPANYID;

          OPEN    OC_BALANCE FOR
          SELECT  ACCOUNTID,ACCOUNTNAME,(nvl(CUMDEBITAMOUNT,0)-nvl(CUMCREDITAMOUNT,0)) BALANCE
          FROM    CASHACCOUNTMASTER
          WHERE   ACCOUNTID=LV_TOCASHACCOUNT
          AND     COMPANYID=LV_LOCATIONID
          AND     LOCATIONID=LV_LOCATIONID

          UNION
          SELECT  ACCOUNTID,ACCOUNTNAME,(nvl(CUMDEBITAMOUNT,0)-nvl(CUMCREDITAMOUNT,0)) BALANCE
          FROM    CASHACCOUNTMASTER
          WHERE   ACCOUNTID=LV_FROMCASHACCOUNT
          AND     COMPANYID=LV_LOCATIONID
          AND     LOCATIONID=LV_LOCATIONID;
      END;
----------------------END ACKNOWLEDGE CASH TRANSFER COUNTER TO COUNTER

----------------------START ACKNOWLEDGE CASH HANDOVER TO COUNTER

 WHEN LV_TRANEVENT='CASHHANDOVERTOCOUNTER' THEN

      DECLARE
            LV_FROMCASHACCOUNT    VARCHAR2(20);
            LV_TOCASHACCOUNT      VARCHAR2(20);
            LV_FROMNONCASHACCOUNT VARCHAR2(20);
            LV_TONONCASHACCOUNT   VARCHAR2(20);
      BEGIN
      UPDATE  TRANSACTION
                SET   ENTRYSTATUS='POSTED',
                      UPDATEDBY=LV_UPDATEDBY,
                      UPDATEDDATE=SYSDATE
                WHERE TRANSACTIONID=LV_TRANSACTIONID;

        SELECT  DISTINCT ACCOUNTNO,TRANAMOUNT INTO LV_TOCASHACCOUNT,LV_TRANAMOUNT
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='DR' AND PAYMENTMODE='CASH';

        SELECT  DISTINCT ACCOUNTNO INTO LV_FROMCASHACCOUNT
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='CR' AND PAYMENTMODE='CASH';

        UPDATE  CASHACCOUNTMASTER
            SET  CUMDEBITAMOUNT=NVL(CUMDEBITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
            WHERE  ACCOUNTID=LV_TOCASHACCOUNT
            AND    LOCATIONID=LV_LOCATIONID
            AND    COMPANYID=LV_COMPANYID;

        UPDATE  CASHACCOUNTMASTER
           SET  CUMCREDITAMOUNT=NVL(CUMCREDITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
          WHERE  ACCOUNTID=LV_FROMCASHACCOUNT
          AND    LOCATIONID=LV_LOCATIONID
          AND    COMPANYID=LV_COMPANYID;


        SELECT  DISTINCT ACCOUNTNO INTO LV_TONONCASHACCOUNT
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='DR' AND PAYMENTMODE!='CASH';
        SELECT  SUM(TRANAMOUNT) INTO LV_TRANAMOUNT FROM TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='DR' AND PAYMENTMODE!='CASH';

        SELECT  DISTINCT ACCOUNTNO INTO LV_FROMNONCASHACCOUNT
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='CR' AND PAYMENTMODE!='CASH';

        UPDATE  CASHACCOUNTMASTER
            SET  CUMDEBITAMOUNT=NVL(CUMDEBITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
            WHERE  ACCOUNTID=LV_TONONCASHACCOUNT
            AND    LOCATIONID=LV_LOCATIONID
            AND    COMPANYID=LV_COMPANYID;

        UPDATE  CASHACCOUNTMASTER
           SET  CUMCREDITAMOUNT=NVL(CUMCREDITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
          WHERE  ACCOUNTID=LV_FROMNONCASHACCOUNT
          AND    LOCATIONID=LV_LOCATIONID
          AND    COMPANYID=LV_COMPANYID;

          OPEN    OC_BALANCE FOR
          SELECT  ACCOUNTID,ACCOUNTNAME,(nvl(CUMDEBITAMOUNT,0)-nvl(CUMCREDITAMOUNT,0)) BALANCE
          FROM    CASHACCOUNTMASTER
          WHERE   ACCOUNTID IN (LV_TOCASHACCOUNT,LV_TONONCASHACCOUNT,LV_FROMCASHACCOUNT,LV_FROMNONCASHACCOUNT)
          AND     COMPANYID=LV_LOCATIONID
          AND     LOCATIONID=LV_LOCATIONID;

      END;

----------------------END ACKNOWLEDGE CASH HANDOVER TO COUNTER

----------------------START ACKNOWLEDGE CASH HANDOVER TO CASHINHAND

 WHEN LV_TRANEVENT='CASHHANDOVERTOCASHINHAND' THEN
       DECLARE
            LV_FROMCASHACCOUNT    VARCHAR2(20);
            LV_CASHINHAND         VARCHAR2(20);
            LV_FROMNONCASHACCOUNT VARCHAR2(20);
            LV_BANKACCOUNT        VARCHAR2(20);
      BEGIN
      UPDATE  TRANSACTION
                SET   ENTRYSTATUS='POSTED',
                      UPDATEDBY=LV_UPDATEDBY,
                      UPDATEDDATE=SYSDATE
                WHERE TRANSACTIONID=LV_TRANSACTIONID;

        SELECT  DISTINCT ACCOUNTNO,TRANAMOUNT INTO LV_CASHINHAND,LV_TRANAMOUNT
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='DR' AND PAYMENTMODE='CASH';

        SELECT  DISTINCT ACCOUNTNO INTO LV_FROMCASHACCOUNT
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='CR' AND PAYMENTMODE='CASH';

       UPDATE  NATURALACCOUNTMASTER
            SET  CUMDEBITAMOUNT=NVL(CUMDEBITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
            WHERE  ACCOUNTID=LV_CASHINHAND
            AND    LOCATIONID=LV_LOCATIONID
            AND    COMPANYID=LV_COMPANYID;

        UPDATE  CASHACCOUNTMASTER
           SET  CUMCREDITAMOUNT=NVL(CUMCREDITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
          WHERE  ACCOUNTID=LV_FROMCASHACCOUNT
          AND    LOCATIONID=LV_LOCATIONID
          AND    COMPANYID=LV_COMPANYID;


        SELECT  DISTINCT ACCOUNTNO INTO LV_BANKACCOUNT
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='DR' AND PAYMENTMODE!='CASH';

       SELECT  SUM(TRANAMOUNT) INTO LV_TRANAMOUNT FROM TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='DR' AND PAYMENTMODE!='CASH';

        SELECT  DISTINCT ACCOUNTNO INTO LV_FROMNONCASHACCOUNT
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='CR' AND PAYMENTMODE!='CASH';

        UPDATE  NATURALACCOUNTMASTER
            SET  CUMDEBITAMOUNT=NVL(CUMDEBITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
            WHERE  ACCOUNTID=LV_BANKACCOUNT
            AND    LOCATIONID=LV_LOCATIONID
            AND    COMPANYID=LV_COMPANYID;

        UPDATE  CASHACCOUNTMASTER
           SET  CUMCREDITAMOUNT=NVL(CUMCREDITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
          WHERE  ACCOUNTID=LV_FROMNONCASHACCOUNT
          AND    LOCATIONID=LV_LOCATIONID
          AND    COMPANYID=LV_COMPANYID;

          OPEN    OC_BALANCE FOR
          SELECT  ACCOUNTID,ACCOUNTNAME,(nvl(CUMDEBITAMOUNT,0)-nvl(CUMCREDITAMOUNT,0)) BALANCE
          FROM    NATURALACCOUNTMASTER
          WHERE   ACCOUNTID IN (LV_CASHINHAND,LV_BANKACCOUNT)
          AND     COMPANYID=LV_LOCATIONID
          AND     LOCATIONID=LV_LOCATIONID

          UNION
          SELECT  ACCOUNTID,ACCOUNTNAME,(nvl(CUMDEBITAMOUNT,0)-nvl(CUMCREDITAMOUNT,0)) BALANCE
          FROM    CASHACCOUNTMASTER
          WHERE   ACCOUNTID IN(LV_FROMCASHACCOUNT,LV_FROMNONCASHACCOUNT)
          AND     COMPANYID=LV_LOCATIONID
          AND     LOCATIONID=LV_LOCATIONID;
      END;
----------------------END ACKNOWLEDGE CASH HANDOVER TO CASHINHAND

 END CASE;


EXCEPTION WHEN OTHERS THEN
DBMS_OUTPUT.put_line(SQLCODE||SQLERRM);
ROLLBACK TO S_SAVEPOINT;

end p_acknowledgecashtocounter;


        """

        target_code = """
SET search_path TO BILLING;

CREATE OR REPLACE PROCEDURE billing.P_ACKNOWLEDGECASHTOCOUNTER (IC_UPDATEACK IN text, OC_BALANCE INOUT refcursor)
LANGUAGE plpgsql
SECURITY DEFINER
AS $BODY$
DECLARE
    
    LV_UPDATEACK xml;
    LV_LOCATIONID billing.NATURALACCOUNTMASTER.LOCATIONID%type;
    LV_COMPANYID billing.NATURALACCOUNTMASTER.COMPANYID%type;
    LV_UPDATEDBY billing.TRANSACTION.UPDATEDBY%type;
    LV_TRANSACTIONID billing.TRANSACTION.TRANSACTIONID%type;
    LV_TRANAMOUNT billing.TRANSACTION.TRANAMOUNT%type;
    LV_TRANEVENT billing.TRANSACTION.TRANEVENT%type;
BEGIN
    SET search_path TO BILLING;
    LV_UPDATEACK := xml(IC_UPDATEACK);
    LV_TRANSACTIONID := TRIM((
        CASE WHEN (
            SELECT
                unnest(xpath('//CashHandlingDetails/@TransactionNo', LV_UPDATEACK)))::text = '' THEN
            NULL
        ELSE
            (
                SELECT
                    unnest(xpath('//CashHandlingDetails/@TransactionNo', LV_UPDATEACK)))
        END)::text);
    LV_LOCATIONID := TRIM((
        CASE WHEN (
            SELECT
                unnest(xpath('//CashHandlingDetails/@LocationID', LV_UPDATEACK)))::text = '' THEN
            NULL
        ELSE
            (
                SELECT
                    unnest(xpath('//CashHandlingDetails/@LocationID', LV_UPDATEACK)))
        END)::text);
    LV_COMPANYID := TRIM((
        CASE WHEN (
            SELECT
                unnest(xpath('//CashHandlingDetails/@CompanyID', LV_UPDATEACK)))::text = '' THEN
            NULL
        ELSE
            (
                SELECT
                    unnest(xpath('//CashHandlingDetails/@CompanyID', LV_UPDATEACK)))
        END)::text);
    LV_UPDATEDBY := TRIM((
        CASE WHEN (
            SELECT
                unnest(xpath('//LoginID/text(', LV_UPDATEACK)))::text)::text = '' THEN
            NULL
        ELSE
            (
                SELECT
                    unnest(xpath('//LoginID/text(', LV_UPDATEACK)))::text)::text
        END);
    SAVEPOINT S_SAVEPOINT;
    SELECT DISTINCT
        TRANEVENT INTO STRICT LV_TRANEVENT
    FROM
        billing.TRANSACTION
    WHERE
        TRANSACTIONID = LV_TRANSACTIONID
        AND ENTRYSTATUS = 'ENTERED'
        AND (REVERSALFLAG = 1
            AND DELETEFLAG = 1);
    CASE
    --START ACKNOWLEDGE CASH TRANSFER CASH IN HAND TO COUNTER
    WHEN LV_TRANEVENT = 'CASHTRANSFERCASHINHANDTOCOUNTER' THEN
        DECLARE LV_CASHACCOUNT varchar(20); LV_CASHINHAND varchar(20);
        BEGIN
            UPDATE
                billing.TRANSACTION
            SET
                ENTRYSTATUS = 'POSTED',
                UPDATEDBY = LV_UPDATEDBY,
                UPDATEDDATE = current_timestamp(0)::timestamp
            WHERE
                TRANSACTIONID = LV_TRANSACTIONID;
                SELECT
                    ACCOUNTNO,
                    TRANAMOUNT INTO STRICT LV_CASHACCOUNT,
                    LV_TRANAMOUNT
                FROM
                    billing.TRANSACTION
                WHERE
                    TRANSACTIONID = LV_TRANSACTIONID
                    AND TRANTYPE = 'DR'; SELECT DISTINCT
                        ACCOUNTNO INTO STRICT LV_CASHINHAND
                    FROM
                        billing.TRANSACTION
                    WHERE
                        TRANSACTIONID = LV_TRANSACTIONID
                        AND TRANTYPE = 'CR'; UPDATE
                            billing.NATURALACCOUNTMASTER
                        SET
                            CUMCREDITAMOUNT = coalesce(CUMCREDITAMOUNT, 0) + LV_TRANAMOUNT,
                            UPDATEDBY = LV_UPDATEDBY,
                            UPDATEDDATE = current_timestamp(0)::timestamp
                        WHERE
                            ACCOUNTID = LV_CASHINHAND
                            AND ACCOUNTTYPE = 'CASHINHAND'
                            AND LOCATIONID = LV_LOCATIONID
                            AND COMPANYID = LV_COMPANYID; UPDATE
                                billing.CASHACCOUNTMASTER
                            SET
                                CUMDEBITAMOUNT = coalesce(CUMDEBITAMOUNT, 0) + LV_TRANAMOUNT,
                                UPDATEDBY = LV_UPDATEDBY,
                                UPDATEDDATE = current_timestamp(0)::timestamp
                            WHERE
                                ACCOUNTID = LV_CASHACCOUNT
                                AND LOCATIONID = LV_LOCATIONID
                                AND COMPANYID = LV_COMPANYID; OPEN OC_BALANCE FOR
                                    SELECT
                                        ACCOUNTID,
                                        ACCOUNTNAME,
                                        (coalesce(CUMDEBITAMOUNT, 0) - coalesce(CUMCREDITAMOUNT, 0)) BALANCE
                                    FROM
                                        billing.NATURALACCOUNTMASTER
                                    WHERE
                                        ACCOUNTID = LV_CASHINHAND
                                        AND COMPANYID = LV_LOCATIONID
                                        AND LOCATIONID = LV_LOCATIONID
                                    UNION
                                    SELECT
                                        ACCOUNTID,
                                        ACCOUNTNAME,
                                        (coalesce(CUMDEBITAMOUNT, 0) - coalesce(CUMCREDITAMOUNT, 0)) BALANCE
                                    FROM
                                        billing.CASHACCOUNTMASTER
                                    WHERE
                                        ACCOUNTID = LV_CASHACCOUNT
                                        AND COMPANYID = LV_LOCATIONID
                                        AND LOCATIONID = LV_LOCATIONID;
            END;
    --END ACKNOWLEDGE CASH TRANSFER CASH IN HAND TO COUNTER
    --START ACKNOWLEDGE CASH TRANSFER COUNTER TO COUNTER
    WHEN LV_TRANEVENT = 'CASHTRANSFERCOUNTERTOCOUNTER' THEN
        DECLARE LV_FROMCASHACCOUNT varchar(20);
    LV_TOCASHACCOUNT varchar(20);
    BEGIN
        UPDATE
            billing.TRANSACTION
        SET
            ENTRYSTATUS = 'POSTED',
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID;
        SELECT
            ACCOUNTNO,
            TRANAMOUNT INTO STRICT LV_FROMCASHACCOUNT,
            LV_TRANAMOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'CR';
        SELECT DISTINCT
            ACCOUNTNO INTO STRICT LV_TOCASHACCOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'DR';
        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMDEBITAMOUNT = coalesce(CUMDEBITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_FROMCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;
        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMDEBITAMOUNT = coalesce(CUMDEBITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_TOCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;
        OPEN OC_BALANCE FOR
            SELECT
                ACCOUNTID,
                ACCOUNTNAME,
                (coalesce(CUMDEBITAMOUNT, 0) - coalesce(CUMCREDITAMOUNT, 0)) BALANCE
            FROM
                billing.CASHACCOUNTMASTER
            WHERE
                ACCOUNTID = LV_TOCASHACCOUNT
                AND COMPANYID = LV_LOCATIONID
                AND LOCATIONID = LV_LOCATIONID
            UNION
            SELECT
                ACCOUNTID,
                ACCOUNTNAME,
                (coalesce(CUMDEBITAMOUNT, 0) - coalesce(CUMCREDITAMOUNT, 0)) BALANCE
            FROM
                billing.CASHACCOUNTMASTER
            WHERE
                ACCOUNTID = LV_FROMCASHACCOUNT
                AND COMPANYID = LV_LOCATIONID
                AND LOCATIONID = LV_LOCATIONID;
    END;
    --END ACKNOWLEDGE CASH TRANSFER COUNTER TO COUNTER
    --START ACKNOWLEDGE CASH HANDOVER TO COUNTER
    WHEN LV_TRANEVENT = 'CASHHANDOVERTOCOUNTER' THEN
        DECLARE LV_FROMCASHACCOUNT varchar(20);
    LV_TOCASHACCOUNT varchar(20);
    LV_FROMNONCASHACCOUNT varchar(20);
    LV_TONONCASHACCOUNT varchar(20);
    BEGIN
        UPDATE
            billing.TRANSACTION
        SET
            ENTRYSTATUS = 'POSTED',
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID;
        SELECT DISTINCT
            ACCOUNTNO,
            TRANAMOUNT INTO STRICT LV_TOCASHACCOUNT,
            LV_TRANAMOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'DR'
            AND PAYMENTMODE = 'CASH';
        SELECT DISTINCT
            ACCOUNTNO INTO STRICT LV_FROMCASHACCOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'CR'
            AND PAYMENTMODE = 'CASH';
        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMDEBITAMOUNT = coalesce(CUMDEBITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_TOCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;
        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMCREDITAMOUNT = coalesce(CUMCREDITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_FROMCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;
        SELECT DISTINCT
            ACCOUNTNO INTO STRICT LV_TONONCASHACCOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'DR'
            AND PAYMENTMODE != 'CASH';
        SELECT
            SUM(TRANAMOUNT) INTO STRICT LV_TRANAMOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'DR'
            AND PAYMENTMODE != 'CASH';
        SELECT DISTINCT
            ACCOUNTNO INTO STRICT LV_FROMNONCASHACCOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'CR'
            AND PAYMENTMODE != 'CASH';
        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMDEBITAMOUNT = coalesce(CUMDEBITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_TONONCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;
        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMCREDITAMOUNT = coalesce(CUMCREDITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_FROMNONCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;
        OPEN OC_BALANCE FOR
            SELECT
                ACCOUNTID,
                ACCOUNTNAME,
                (coalesce(CUMDEBITAMOUNT, 0) - coalesce(CUMCREDITAMOUNT, 0)) BALANCE
            FROM
                billing.CASHACCOUNTMASTER
            WHERE
                ACCOUNTID IN (LV_TOCASHACCOUNT, LV_TONONCASHACCOUNT, LV_FROMCASHACCOUNT, LV_FROMNONCASHACCOUNT)
                AND COMPANYID = LV_LOCATIONID
                AND LOCATIONID = LV_LOCATIONID;
    END;
    --END ACKNOWLEDGE CASH HANDOVER TO COUNTER
    --START ACKNOWLEDGE CASH HANDOVER TO CASHINHAND
    WHEN LV_TRANEVENT = 'CASHHANDOVERTOCASHINHAND' THEN
        DECLARE LV_FROMCASHACCOUNT varchar(20);
    LV_CASHINHAND varchar(20);
    LV_FROMNONCASHACCOUNT varchar(20);
    LV_BANKACCOUNT varchar(20);
    BEGIN
        UPDATE
            billing.TRANSACTION
        SET
            ENTRYSTATUS = 'POSTED',
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID;
        SELECT DISTINCT
            ACCOUNTNO,
            TRANAMOUNT INTO STRICT LV_CASHINHAND,
            LV_TRANAMOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'DR'
            AND PAYMENTMODE = 'CASH';
        SELECT DISTINCT
            ACCOUNTNO INTO STRICT LV_FROMCASHACCOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'CR'
            AND PAYMENTMODE = 'CASH';
        UPDATE
            billing.NATURALACCOUNTMASTER
        SET
            CUMDEBITAMOUNT = coalesce(CUMDEBITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_CASHINHAND
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;
        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMCREDITAMOUNT = coalesce(CUMCREDITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_FROMCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;
        SELECT DISTINCT
            ACCOUNTNO INTO STRICT LV_BANKACCOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'DR'
            AND PAYMENTMODE != 'CASH';
        SELECT
            SUM(TRANAMOUNT) INTO STRICT LV_TRANAMOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'DR'
            AND PAYMENTMODE != 'CASH';
        SELECT DISTINCT
            ACCOUNTNO INTO STRICT LV_FROMNONCASHACCOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'CR'
            AND PAYMENTMODE != 'CASH';
        UPDATE
            billing.NATURALACCOUNTMASTER
        SET
            CUMDEBITAMOUNT = coalesce(CUMDEBITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_BANKACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;
        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMCREDITAMOUNT = coalesce(CUMCREDITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_FROMNONCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;
        OPEN OC_BALANCE FOR
            SELECT
                ACCOUNTID,
                ACCOUNTNAME,
                (coalesce(CUMDEBITAMOUNT, 0) - coalesce(CUMCREDITAMOUNT, 0)) BALANCE
            FROM
                billing.NATURALACCOUNTMASTER
            WHERE
                ACCOUNTID IN (LV_CASHINHAND, LV_BANKACCOUNT)
                AND COMPANYID = LV_LOCATIONID
                AND LOCATIONID = LV_LOCATIONID
            UNION
            SELECT
                ACCOUNTID,
                ACCOUNTNAME,
                (coalesce(CUMDEBITAMOUNT, 0) - coalesce(CUMCREDITAMOUNT, 0)) BALANCE
            FROM
                billing.CASHACCOUNTMASTER
            WHERE
                ACCOUNTID IN (LV_FROMCASHACCOUNT, LV_FROMNONCASHACCOUNT)
                AND COMPANYID = LV_LOCATIONID
                AND LOCATIONID = LV_LOCATIONID;
    END;
    --END ACKNOWLEDGE CASH HANDOVER TO CASHINHAND
    END CASE;
       -- END;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '% %', sqlstate, sqlerrm;
        --ROLLBACK TO S_SAVEPOINT;
END;

$BODY$;





               """

        deployment_error = """
  SQL Error [42601]: ERROR: mismatched parentheses at or near ")"

Error position: line: 57 pos: 2012
        """

        llm = setup_application()
        run_workflow(llm, source_code, target_code, deployment_error)

        print("\n🎉 Execution completed successfully!")

    except ValueError as e:
        print(f"\nConfiguration Error: {str(e)}")
        print("\nPlease check your configuration and try again.")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected Error: {str(e)}")
        print("\nPlease report this issue with the error details above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
