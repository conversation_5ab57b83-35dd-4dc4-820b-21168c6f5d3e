import os
import sys
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from contextlib import asynccontextmanager
from dotenv import load_dotenv

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.routes import migration_router
from core.config import ConfigManager

# Load environment variables
load_dotenv()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown events."""
    # Startup
    print("🚀 Starting QMigrator AI FastAPI Application...")
    
    # Initialize configuration
    try:
        config_manager = ConfigManager()
        config_manager.load_from_env()
        app.state.config_manager = config_manager
        
        source_db = config_manager.get_source_database()
        target_db = config_manager.get_target_database()
        migration_direction = config_manager.get_migration_direction()
        
        print(f"✅ Configuration loaded: {migration_direction} Migration")
        print(f"📊 Source Database: {source_db}")
        print(f"🎯 Target Database: {target_db}")
        
    except Exception as e:
        print(f"❌ Configuration Error: {str(e)}")
        print("Please ensure SOURCE_DATABASE and TARGET_DATABASE environment variables are set")
        raise
    
    yield
    
    # Shutdown
    print("🔄 Shutting down QMigrator AI FastAPI Application...")

# Create FastAPI application
app = FastAPI(
    title="QMigrator AI - Database Migration API",
    description="""
    🔄 **QMigrator AI** - Intelligent Database Migration Assistant
    
    This API provides AI-powered database code conversion with real-time progress tracking.
    
    ## Features
    
    * **Multi-LLM Support**: Azure OpenAI, OpenAI, Anthropic, Groq, Gemini, Ollama
    * **Intelligent Error Resolution**: AI-driven error identification and correction
    * **Real-time Progress**: Track migration workflow progress
    * **Comprehensive Validation**: Multi-phase validation for accuracy
    * **Audit Trail**: Complete tracking of all changes and iterations
    
    ## Workflow Steps
    
    1. **Split Statements** - Parse source and target code into individual statements
    2. **Error Analysis** - Identify problematic statements using hybrid AI/position-based approach
    3. **Source Mapping** - Map target statements to corresponding source statements
    4. **Statement Conversion** - Convert statements using AI with source context
    5. **Validation & Deployment** - Validate conversions and deploy to target database
    6. **Iterative Improvement** - Repeat until successful deployment
    
    ## Getting Started
    
    1. Configure your LLM provider in environment variables
    2. Use `/migrate/start` endpoint to begin migration
    3. Monitor progress with `/migrate/status/{job_id}` endpoint
    4. Retrieve results with `/migrate/result/{job_id}` endpoint
    """,
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(migration_router, prefix="/api/v1", tags=["Migration"])

@app.get("/", response_class=HTMLResponse)
async def root():
    """Root endpoint with welcome message and navigation."""
    config_manager = app.state.config_manager
    source_db = config_manager.get_source_database()
    target_db = config_manager.get_target_database()
    migration_direction = config_manager.get_migration_direction()
    
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>QMigrator AI - Database Migration API</title>
        <style>
            body {{ 
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                margin: 0; 
                padding: 20px; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                min-height: 100vh;
            }}
            .container {{ 
                max-width: 800px; 
                margin: 0 auto; 
                background: rgba(255,255,255,0.1); 
                padding: 40px; 
                border-radius: 15px;
                backdrop-filter: blur(10px);
                box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            }}
            h1 {{ 
                color: #fff; 
                text-align: center; 
                margin-bottom: 30px;
                font-size: 2.5em;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }}
            .config-info {{ 
                background: rgba(255,255,255,0.2); 
                padding: 20px; 
                border-radius: 10px; 
                margin: 20px 0;
                border-left: 4px solid #4CAF50;
            }}
            .nav-links {{ 
                display: flex; 
                justify-content: center; 
                gap: 20px; 
                margin: 30px 0;
                flex-wrap: wrap;
            }}
            .nav-link {{ 
                background: rgba(255,255,255,0.2); 
                color: white; 
                padding: 12px 24px; 
                text-decoration: none; 
                border-radius: 25px; 
                transition: all 0.3s ease;
                border: 2px solid transparent;
            }}
            .nav-link:hover {{ 
                background: rgba(255,255,255,0.3); 
                transform: translateY(-2px);
                border-color: rgba(255,255,255,0.5);
            }}
            .feature {{ 
                margin: 15px 0; 
                padding: 10px 0;
                border-bottom: 1px solid rgba(255,255,255,0.2);
            }}
            .feature:last-child {{ border-bottom: none; }}
            .emoji {{ font-size: 1.2em; margin-right: 10px; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔄 QMigrator AI</h1>
            <p style="text-align: center; font-size: 1.2em; margin-bottom: 30px;">
                Intelligent Database Migration Assistant
            </p>
            
            <div class="config-info">
                <h3>📊 Current Configuration</h3>
                <p><strong>Migration Type:</strong> {migration_direction}</p>
                <p><strong>Source Database:</strong> {source_db}</p>
                <p><strong>Target Database:</strong> {target_db}</p>
            </div>
            
            <div class="nav-links">
                <a href="/docs" class="nav-link">📚 API Documentation</a>
                <a href="/redoc" class="nav-link">📖 ReDoc</a>
                <a href="/streamlit" class="nav-link">🖥️ Streamlit UI</a>
            </div>
            
            <h3>✨ Key Features</h3>
            <div class="feature">
                <span class="emoji">🤖</span><strong>Multi-LLM Support:</strong> Azure OpenAI, OpenAI, Anthropic, Groq, Gemini, Ollama
            </div>
            <div class="feature">
                <span class="emoji">🔍</span><strong>Intelligent Error Resolution:</strong> AI-driven error identification and correction
            </div>
            <div class="feature">
                <span class="emoji">📊</span><strong>Real-time Progress:</strong> Track migration workflow progress
            </div>
            <div class="feature">
                <span class="emoji">✅</span><strong>Comprehensive Validation:</strong> Multi-phase validation for accuracy
            </div>
            <div class="feature">
                <span class="emoji">📋</span><strong>Audit Trail:</strong> Complete tracking of all changes and iterations
            </div>
            
            <p style="text-align: center; margin-top: 30px; opacity: 0.8;">
                Ready to migrate your database code with AI assistance!
            </p>
        </div>
    </body>
    </html>
    """

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "QMigrator AI FastAPI",
        "version": "1.0.0"
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
