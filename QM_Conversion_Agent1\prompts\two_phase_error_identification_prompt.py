"""
Two-phase prompts for error identification in database conversion.
"""
from typing import List
from utils.database_names import get_database_specific_terms

def create_phase1_error_identification_prompt(target_statements: List[str], error_message: str, previous_feedback: str = None) -> str:
    """
    Phase 1: Identify the primary error statement causing the deployment error.
    """
    # Get dynamic database names
    db_terms = get_database_specific_terms()
    source_db = db_terms['source_db']
    target_db = db_terms['target_db']
    expert_title = db_terms['expert_title']
    migration_direction = db_terms['migration_direction']
    statements_text = "\n".join([f"{i+1}. {stmt.strip()}" for i, stmt in enumerate(target_statements)])

    # Add feedback section if provided
    feedback_section = ""
    if previous_feedback:
        feedback_section = f"""
PREVIOUS IDENTIFICATION FEEDBACK:
The previous error identification was rejected with this feedback:
{previous_feedback}

Please address these specific issues:
- Review the error analysis more carefully
- Consider different error patterns and root causes
- Ensure accurate line number and statement matching
- Focus on the exact error described in the message
- Provide more detailed technical reasoning

"""

    return f"""You are a {expert_title} with deep expertise in both {source_db} and {target_db} database systems. Your task is to identify the PRIMARY statement causing the deployment error during {migration_direction} migration.

{feedback_section}

ERROR MESSAGE:
{error_message}

TARGET STATEMENTS:
{statements_text}

PHASE 1 ANALYSIS - PRIMARY ERROR IDENTIFICATION:
Focus on identifying the ONE statement that is directly causing the reported error.

STEP 1: ERROR MESSAGE ANALYSIS
- Parse the error message for specific details: error type, line numbers, character positions
- Identify the exact error category (syntax, type, constraint, etc.)
- Extract key error indicators and patterns

STEP 2: STATEMENT-BY-STATEMENT ANALYSIS
- Systematically examine each statement for patterns matching the error
- Look for specific syntax issues, type mismatches, or constraint violations
- Pay attention to {source_db}-specific constructs that don't work in {target_db}

STEP 3: PRIMARY ERROR IDENTIFICATION
- Identify the ONE statement that directly matches the error description
- Ensure the statement contains the exact issue described in the error message
- Verify that this statement would cause the specific error when executed
- Consider that some statements may be target database-specific constructs with no source database equivalent

ERROR PATTERN MATCHING CRITERIA:
- Data Type Issues: Database-specific type differences and conversions
- Syntax Differences: {source_db}-specific functions, operators, keywords
- Constraint Violations: NOT NULL, CHECK, FOREIGN KEY, UNIQUE constraints
- Function/Procedure Issues: RETURN statements, parameter handling, OUT parameters
- Control Flow Issues: IF/ELSE, LOOP, EXCEPTION handling syntax differences
- Transaction Issues: COMMIT, ROLLBACK, SAVEPOINT handling
- Cursor Issues: OPEN, FETCH, CLOSE cursor operations
- Index/Optimization: Hints, index syntax differences

CONFIDENCE SCORING:
- 0.9-1.0: Exact match with error message details
- 0.8-0.9: Strong correlation with error pattern
- 0.7-0.8: Good match with some uncertainty
- 0.6-0.7: Moderate match requiring validation
- Below 0.6: Weak match, needs reconsideration

OUTPUT FORMAT (JSON):
{{
  "error_statement_number": <integer>,
  "confidence_score": <float between 0.0 and 1.0>,
  "reasoning": "<comprehensive analysis including: 1) Specific error type and category identification, 2) Detailed analysis of why this statement matches the error pattern, 3) Line-by-line breakdown of the problematic statement, 4) Explanation of the {migration_direction} conversion issue, 5) Technical details about the database operation causing the error, 6) Comparison with other statements to explain why they were ruled out, 7) Specific syntax or semantic issue identification>"
}}

IMPORTANT NOTES:
- Focus ONLY on identifying the primary error statement
- Provide detailed technical reasoning for your choice
- Ensure high confidence in your identification
- The statement number must be between 1 and {len(target_statements)}"""


def create_phase2_error_context_prompt(target_statements: List[str], identified_error_statement: int, error_message: str) -> str:
    """
    Phase 2: Create error context around the identified error statement.
    """
    # Get dynamic database names
    db_terms = get_database_specific_terms()
    source_db = db_terms['source_db']
    target_db = db_terms['target_db']
    expert_title = db_terms['expert_title']

    statements_text = "\n".join([f"{i+1}. {stmt.strip()}" for i, stmt in enumerate(target_statements)])

    return f"""You are a {expert_title} with deep expertise in both {source_db} and {target_db} database systems. You have already identified that target statement #{identified_error_statement} is causing the deployment error.

Now create the ERROR CONTEXT around this identified error statement.

ERROR MESSAGE:
{error_message}

TARGET STATEMENTS:
{statements_text}

IDENTIFIED ERROR STATEMENT:
- Statement #{identified_error_statement} has been identified as the primary error statement

PHASE 2 ANALYSIS - ERROR CONTEXT CREATION:
Create a 3-statement context around the identified error statement.

STEP 1: VERIFY ERROR STATEMENT
- Confirm statement #{identified_error_statement} is the correct error statement
- Ensure it matches the error message details

STEP 2: CONTEXT BOUNDARY DETERMINATION
- Identify the statement immediately before the error (before_error)
- Identify the statement immediately after the error (after_error)
- Handle edge cases for first/last statements
- Recognize that some statements may be target database-specific with no source equivalent

STEP 3: CONTEXT VALIDATION
- Ensure the context provides meaningful surrounding information
- Verify the before/after statements are logically related to the error statement
- Confirm the context will be useful for error analysis and fixing

CONTEXT CREATION RULES:
1. before_error: Statement #{identified_error_statement - 1} (if exists, otherwise 0)
2. error_statement: Statement #{identified_error_statement}
3. after_error: Statement #{identified_error_statement + 1} (if exists, otherwise 0)

EDGE CASE HANDLING:
- If error statement is #1: before_error = 0, error_statement = 1, after_error = 2
- If error statement is last: before_error = last-1, error_statement = last, after_error = 0
- If only one statement: before_error = 0, error_statement = 1, after_error = 0

OUTPUT FORMAT (JSON):
{{
  "target_statements": [
    {{
      "target_statement_number": <integer for before_error (0 if none)>,
      "statement_type": "before_error"
    }},
    {{
      "target_statement_number": {identified_error_statement},
      "statement_type": "error_statement"
    }},
    {{
      "target_statement_number": <integer for after_error (0 if none)>,
      "statement_type": "after_error"
    }}
  ],
  "validation_notes": "<analysis including: 1) Confirmation of error statement accuracy, 2) Assessment of context relevance and completeness, 3) Evaluation of before/after statement logical relationship, 4) Any edge case handling applied, 5) Context quality assessment for error analysis>"
}}

IMPORTANT NOTES:
- The error_statement must be #{identified_error_statement}
- Use 0 for before_error or after_error if they don't exist
- Ensure all statement numbers are valid (between 0 and {len(target_statements)})
- Provide comprehensive validation notes"""
