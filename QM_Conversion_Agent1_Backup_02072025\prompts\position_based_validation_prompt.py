"""
Prompts for position-based error identification validation in database conversion.
"""
from typing import Dict

def create_position_based_validation_prompt(target_error_context: Dict, error_message: str) -> str:
    """
    Creates an ultra-simple validation prompt: Does this statement cause the deployment error?

    This function creates a prompt that asks ONE simple question:
    "Would running this statement in PostgreSQL produce this exact deployment error?"

    Args:
        target_error_context: Dictionary containing the error context (before, error, after statements)
        error_message: The error message from deployment

    Returns:
        A formatted prompt string asking only if this statement causes the deployment error
    """
    return f"""You are an Oracle to PostgreSQL Database Migration Expert with deep expertise in both Oracle and PostgreSQL database systems. Your task is to validate if the position-based identified statement is causing the reported error during Oracle to PostgreSQL migration.

ERROR MESSAGE:
{error_message}

POSITION-BASED IDENTIFIED CONTEXT:
Before Error (#{target_error_context.before_statement_number}):
{target_error_context.before_statement}

Error Statement (#{target_error_context.error_statement_number}):
{target_error_context.error_statement}

After Error (#{target_error_context.after_statement_number}):
{target_error_context.after_statement}

SIMPLE QUESTION:
Does this statement cause the deployment error? YES or NO?

ONLY CHECK:
- Would running this statement in PostgreSQL produce this exact deployment error?

THAT'S IT. Nothing else.

OUTPUT FORMAT (JSON):
{{
  "is_correct": true/false,
  "confidence": <float between 0-1>,
  "explanation": "<one sentence: does this statement cause this deployment error? yes/no and why>"
}}

FOCUS: Only answer - does this statement cause the deployment error?"""
