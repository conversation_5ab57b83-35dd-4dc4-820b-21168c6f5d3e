"""
Position-based error mapping utilities for precise SQL statement identification in database migration workflows.

This module provides sophisticated error position mapping capabilities for Oracle to PostgreSQL
migration workflows, enabling precise identification of problematic SQL statements based on
database error messages containing line numbers or character positions.
"""

import re
from typing import List, Tuple, Optional, Dict, Any
from formatting.sql_splitter import split_sql_statements


class UniversalErrorExtractor:
    """
    Universal database error message parser for extracting position information.

    This class provides comprehensive parsing capabilities for database error messages
    from various database systems (PostgreSQL, Oracle, SQL Server, etc.) to extract
    line numbers, character positions, and other location information for precise
    error statement identification.

    Supported Error Formats:
        - PostgreSQL: "LINE 139:", "position 15420"
        - Oracle: "line 447", "at line 184"
        - SQL Server: "Line 25", "position 1024"
        - Generic: Various line/position formats
    """

    def extract_position_info(self, error_message: str) -> Dict[str, int]:
        """
        Extract line or position information from database error messages.

        This method uses regex patterns to identify and extract position information
        from various database error message formats. It prioritizes line numbers over
        character positions for more reliable statement identification.

        Supported Patterns:
            - PostgreSQL LINE format: "LINE 139:"
            - Position format: "Position: 17013" or "position 15420"
            - Line format: "line: 447" or "line 184"
            - Case-insensitive matching for all patterns

        Args:
            error_message (str): Database error message containing position information

        Returns:
            Dict[str, int]: Dictionary containing extracted position information
                          Keys: 'line' for line numbers, 'position' for character positions
                          Empty dict if no position information found

        Example:
            >>> extractor = UniversalErrorExtractor()
            >>> info = extractor.extract_position_info("ERROR: syntax error at LINE 139:")
            >>> print(info)  # {'line': 139}
        """
        position_info = {}

        # PostgreSQL line format: "LINE 139:" (highest priority)
        line_match = re.search(r'LINE\s+(\d+):', error_message, re.IGNORECASE)
        if line_match:
            position_info['line'] = int(line_match.group(1))
            return position_info

        # Position format: "Position: 17013" or "position 15420"
        pos_match = re.search(r'position[:\s]+(\d+)', error_message, re.IGNORECASE)
        if pos_match:
            position_info['position'] = int(pos_match.group(1))
            return position_info

        # Line format: "line: 447" or "line 184"
        line_match2 = re.search(r'line[:\s]+(\d+)', error_message, re.IGNORECASE)
        if line_match2:
            position_info['line'] = int(line_match2.group(1))
            return position_info

        return position_info


class PositionTracker:
    """Track actual positions during SQL splitting process"""

    def __init__(self, sql_code: str):
        self.sql_code = sql_code
        self.positions = []  # List of (start_pos, end_pos) for each statement

    def split_with_positions(self) -> List[Tuple[int, int]]:
        """Split SQL and track actual positions using the same logic as sql_splitter"""
        if not self.sql_code or not self.sql_code.strip():
            return []

        # Import and use the actual SQL splitter to get the statements
        statements = split_sql_statements(self.sql_code)

        # Now find the positions of these statements in the original SQL
        positions = []
        search_start = 0

        for statement in statements:
            # Clean the statement for searching
            clean_stmt = statement.strip()
            if not clean_stmt:
                continue

            # Find the statement in the original SQL starting from search_start
            stmt_pos = self.sql_code.find(clean_stmt, search_start)

            if stmt_pos != -1:
                # Found the statement
                stmt_start = stmt_pos
                stmt_end = stmt_pos + len(clean_stmt) - 1
                positions.append((stmt_start, stmt_end))
                search_start = stmt_end + 1
            else:
                # Try to find a partial match or use approximate position
                # This handles cases where the statement might have been modified during splitting
                approximate_start = search_start
                approximate_end = approximate_start + len(clean_stmt)
                positions.append((approximate_start, approximate_end))
                search_start = approximate_end + 1

        return positions

    def is_in_comment_or_quotes(self, pos: int, code: str) -> bool:
        """Check if position is within comment or quotes"""
        # Simple check for comments and quotes
        before_pos = code[:pos]

        # Check for single quotes
        single_quotes = before_pos.count("'")
        if single_quotes % 2 == 1:  # Odd number means we're inside quotes
            return True

        # Check for comments
        if '--' in before_pos:
            last_comment = before_pos.rfind('--')
            last_newline = before_pos.rfind('\n', 0, last_comment)
            if last_newline < last_comment:  # Comment is on same line
                return True

        return False


class AdvancedPositionMapper:
    """Advanced position mapping with comprehensive statement tracking"""
    
    def __init__(self):
        self.statement_ranges = []  # List of (start_pos, end_pos, start_line, end_line)
        self.position_to_statement = {}  # char_pos -> statement_number
        self.line_to_statement = {}  # line_number -> statement_number
        self.statement_content_hash = {}  # statement_number -> content_hash
        self.original_sql = ""
        self.lines = []
    
    def split_with_comprehensive_mapping(self, sql_code: str) -> Tuple[List[str], 'AdvancedPositionMapper']:
        """Split SQL and create comprehensive position mapping"""
        self.original_sql = sql_code
        self.lines = sql_code.split('\n')

        # Use the existing SQL splitter
        statements = split_sql_statements(sql_code)

        # Create position mapping
        self.create_position_mapping(sql_code, statements)

        return statements, self

    def create_position_mapping(self, sql_code: str, statements: List[str]):
        """Create comprehensive position mapping using sequential tracking"""
        # Re-split the SQL to track actual positions during splitting process
        position_tracker = PositionTracker(sql_code)
        tracked_statements = position_tracker.split_with_positions()

        # Ensure we have matching statement counts
        if len(statements) != len(tracked_statements):
            # Position tracking failed - do not create any mappings
            # This ensures clean fallback to AI without false position matches
            return

        # Create precise mapping using tracked positions
        for stmt_num, (statement, (stmt_start, stmt_end)) in enumerate(zip(statements, tracked_statements), 1):
            # Calculate line numbers
            lines_before = sql_code[:stmt_start].count('\n')
            lines_in_stmt = statement.count('\n')
            start_line = lines_before + 1
            end_line = start_line + lines_in_stmt

            # Store range
            self.statement_ranges.append((stmt_start, stmt_end, start_line, end_line))

            # Map positions to statement
            for pos in range(stmt_start, stmt_end + 1):
                self.position_to_statement[pos] = stmt_num

            # Map lines to statement
            for line in range(start_line, end_line + 1):
                self.line_to_statement[line] = stmt_num

            # Create content hash for duplicate detection
            content_hash = hash(statement.strip().lower())
            self.statement_content_hash[stmt_num] = content_hash

    def has_valid_position_mapping(self) -> bool:
        """Check if position mapping was successfully created"""
        return len(self.position_to_statement) > 0 or len(self.line_to_statement) > 0

    def find_statement_by_any_position(self, position_info: Dict[str, int]) -> List[Tuple[int, str, int]]:
        """Find statement by line or character position"""
        candidates = []
        
        if 'line' in position_info:
            line_num = position_info['line']
            if line_num in self.line_to_statement:
                stmt_num = self.line_to_statement[line_num]
                candidates.append((stmt_num, 'line', line_num))
        
        if 'position' in position_info:
            char_pos = position_info['position']
            if char_pos in self.position_to_statement:
                stmt_num = self.position_to_statement[char_pos]
                candidates.append((stmt_num, 'position', char_pos))
        
        return candidates
    
    def get_duplicate_statements(self, statement_num: int) -> List[int]:
        """Get all statements with same content hash (duplicates)"""
        if statement_num not in self.statement_content_hash:
            return [statement_num]
        
        target_hash = self.statement_content_hash[statement_num]
        duplicates = []
        
        for stmt_num, content_hash in self.statement_content_hash.items():
            if content_hash == target_hash:
                duplicates.append(stmt_num)
        
        return sorted(duplicates)
    
    def to_dict(self) -> Dict[str, Any]:
        """Serialize to dictionary with proper key handling"""
        return {
            'statement_ranges': self.statement_ranges,
            'position_to_statement': {str(k): v for k, v in self.position_to_statement.items()},
            'line_to_statement': {str(k): v for k, v in self.line_to_statement.items()},
            'statement_content_hash': {str(k): v for k, v in self.statement_content_hash.items()},
            'original_sql': self.original_sql,
            'lines': self.lines
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AdvancedPositionMapper':
        """Deserialize from dictionary with proper key conversion"""
        if not data:
            raise ValueError("Cannot deserialize from empty data")

        mapper = cls()

        # Direct access without defaults - will raise KeyError if missing
        try:
            mapper.statement_ranges = data['statement_ranges']

            # Convert string keys back to integers
            mapper.position_to_statement = {int(k): v for k, v in data['position_to_statement'].items()}
            mapper.line_to_statement = {int(k): v for k, v in data['line_to_statement'].items()}
            mapper.statement_content_hash = {int(k): v for k, v in data['statement_content_hash'].items()}

            mapper.original_sql = data['original_sql']
            mapper.lines = data['lines']
        except KeyError as e:
            raise ValueError(f"Missing required key in position mapper data: {e}")
        except (ValueError, TypeError) as e:
            raise ValueError(f"Invalid data format in position mapper: {e}")

        return mapper


class SmartStatementResolver:
    """Simple position-based statement resolver"""
    
    def __init__(self, position_mapper: AdvancedPositionMapper):
        self.position_mapper = position_mapper
    
    def resolve_statement_by_position(self, error_message: str, iteration_count: int = 1) -> Optional[int]:
        """Simple position-based statement resolution with clean AI fallback"""

        # Check if position mapping is valid before attempting resolution
        if not self.position_mapper.has_valid_position_mapping():
            return None

        # Extract position information
        extractor = UniversalErrorExtractor()
        position_info = extractor.extract_position_info(error_message)

        if not position_info:
            return None

        # Get candidate statements
        candidates = self.position_mapper.find_statement_by_any_position(position_info)

        if not candidates:
            return None

        # Handle multiple candidates (duplicates)
        if len(candidates) == 1:
            statement_num = candidates[0][0]
            return statement_num

        # Multiple candidates - use iteration to select different ones
        # Get duplicates for the first candidate
        first_candidate = candidates[0][0]
        duplicates = self.position_mapper.get_duplicate_statements(first_candidate)

        if len(duplicates) > 1:
            # Rotate through duplicates based on iteration
            selected_index = (iteration_count - 1) % len(duplicates)
            selected_statement = duplicates[selected_index]
            return selected_statement

        # No duplicates, just return first candidate
        statement_num = candidates[0][0]
        return statement_num
