# QMigrator AI - Oracle to PostgreSQL Migration System
## Developer Guide

### Table of Contents
1. [System Overview](#system-overview)
2. [Architecture & Components](#architecture--components)
3. [Workflow Analysis](#workflow-analysis)
4. [Code Structure & Organization](#code-structure--organization)
5. [AI Integration & Pydantic Models](#ai-integration--pydantic-models)
6. [Development Guidelines](#development-guidelines)

---

## System Overview

QMigrator AI is an AI-powered workflow system designed to automate Oracle to PostgreSQL database migration through intelligent error analysis, statement mapping, and iterative correction. The system uses AI models to understand deployment errors, map Oracle source code to PostgreSQL target code, and generate corrected statements until successful deployment.

### Key Features
- **AI-Driven Analysis**: Uses multiple LLM providers for SQL understanding
- **Hybrid Error Detection**: Combines position-based resolution with AI validation
- **Iterative Improvement**: Automatically loops until successful PostgreSQL deployment
- **Real Database Testing**: Deploys to actual PostgreSQL for validation
- **Audit Trail**: Maintains history with Excel and SQL file outputs
- **Multi-Layer Validation**: AI validates each critical step with feedback integration
- **Web Interface**: Streamlit-based user interface for interactive migration

### Business Value
- **Reduces Migration Time**: Automates complex Oracle to PostgreSQL conversions
- **Improves Accuracy**: AI-driven analysis reduces human error in SQL conversion
- **Provides Transparency**: Complete audit trail for compliance and debugging

---

## Architecture & Components

### Core Technology Stack
- **LangGraph**: Workflow orchestration with state management and conditional routing
- **Pydantic**: Type-safe data models for AI structured outputs
- **Multiple LLM Providers**: OpenAI, Azure OpenAI, Anthropic, Groq, Gemini, Ollama
- **PostgreSQL**: Real database deployment and validation
- **Streamlit**: Web-based user interface for interactive migration
- **Python**: Core implementation with comprehensive error handling

### System Components

#### 1. Configuration Management (`config/`)
- **ConfigManager**: Centralized configuration with environment variable support
- **ModelConfig**: Provider-specific LLM configurations with validation

#### 2. Language Model Integration (`llm/`)
- **Multi-Provider Support**: Flexible AI provider integration
- **Structured Outputs**: Pydantic model integration for reliable AI responses
- **Error Handling**: Robust error handling for AI provider failures

#### 3. State Management (`state/`)
- **WorkflowState**: Comprehensive Pydantic model for workflow state
- **AI Response Models**: Structured models for AI outputs with validation
- **Type Safety**: Full type checking for reliable data flow

#### 4. Workflow Orchestration (`workflow/`)
- **GraphBuilder**: LangGraph workflow construction with conditional routing
- **Conditional Logic**: Intelligent routing based on validation results

#### 5. Migration Nodes (`nodes/`)
- **UniversalCodeMigrationNodes**: Complete suite of migration operations
- **AI Integration**: Structured AI calls with confidence scoring
- **Validation Loops**: Multi-layer validation with feedback integration

#### 6. Utilities (`utils/`)
- **Error Position Mapping**: Precise error position mapping for statement identification
- **SQL Formatting**: Statement splitting and formatting utilities

#### 7. Prompts (`prompts/`)
- **Specialized Prompts**: AI prompts optimized for each workflow step
- **Context-Aware**: Prompts adapted for different migration scenarios
- **Validation Focused**: Prompts designed for reliable AI validation

#### 8. User Interface (`streamlit_main.py`)
- **Web Interface**: Streamlit-based interactive migration interface
- **Real-time Progress**: Visual workflow progress tracking
- **Input Management**: Code input and error message handling

---

## Workflow Analysis

### Workflow Architecture
The QMigrator AI workflow consists of 10 specialized nodes that handle the complete Oracle to PostgreSQL migration process:

```
START → Split Statements → Identify Error → Validate Error → Map Source →
Validate Mapping → Convert Statements → Validate Conversion → Replace Statements →
Deploy Code → Check Status → (END or ITERATE)
```

### Implemented Workflow Nodes

#### 1. Split Statements (`splitStatments`)
**Purpose**: Parse SQL code into individual statements for granular analysis

**Implementation**:
- Splits Oracle source code and PostgreSQL target code into numbered statements
- Creates position mapping for error location
- Optimizes for loop iterations by reusing source statements
- Generates Excel files for audit trail

**Key Features**:
- SQL statement parsing with comment and quote handling
- Position tracking for error resolution
- Iteration-based file naming

#### 2. Analyze Error & Identify Target Statements (`AnalyzeError_identifyTargetStatements`)
**Purpose**: Identify which specific target statement is causing the deployment error

**Hybrid Approach**:
1. **Position-Based Resolution**: Uses error line/position information from PostgreSQL errors
2. **AI Validation**: Validates if position-identified statement causes the exact error
3. **AI Fallback**: Two-phase AI analysis if position-based approach fails

**Implementation**:
- Extracts line/position from deployment error messages
- Maps error position to specific target statement
- Creates error context (before/error/after statements)
- Uses AI for validation and fallback analysis

#### 3. Validate Error Identification (`validate_error_identification`)
**Purpose**: AI validates that the correct error statement was identified

**Implementation**:
- Uses AI with confidence scoring for validation
- Generates feedback for failed validations
- Tracks validation attempts for quality control
- Incorporates feedback into next identification attempt

#### 4. Map Source with Target Statements (`mapSource_withTargetStatements`)
**Purpose**: Maps PostgreSQL error statements back to corresponding Oracle source statements

**Implementation**:
- AI identifies Oracle statements achieving same business outcome
- Creates sequential mapping around error statement
- Handles database-specific statements by mapping to 0 (no source equivalent)
- Focuses on functional equivalence over syntax similarity

#### 5. Validate Source Mapping (`validate_source_mapping`)
**Purpose**: AI validates the accuracy of source-to-target statement mapping

**Implementation**:
- Validates functional equivalence between source and target
- Confirms database-specific statements are correctly unmapped
- Generates feedback for mapping improvement
- Optimizes by skipping validation for PostgreSQL-specific code

#### 6. Convert Target Statement (`Convert_TargetStatement`)
**Purpose**: AI converts problematic PostgreSQL statements using dynamic strategy

**Implementation**:
- Uses Oracle source context for business logic understanding
- Applies PostgreSQL expertise for database-specific statements
- Structured outputs with detailed change tracking
- Incorporates feedback from previous validation failures

#### 7. Validate Conversion (`validate_conversion`)
**Purpose**: AI validates that generated corrections are accurate

**Implementation**:
- Validates functional equivalence with Oracle source
- Checks appropriate PostgreSQL syntax and features
- Ensures corrections address deployment errors
- Generates feedback for failed validations

#### 8. Replace Target Statement (`replaceTargetStatement`)
**Purpose**: Prepares AI corrections for deployment phase

**Implementation**:
- Passes AI corrections directly to deployment
- Maintains state consistency for deployment processing
- Preserves iteration tracking for audit trail

#### 9. Deploy Target Code (`targetcode_deployment`)
**Purpose**: Applies corrections and deploys to PostgreSQL database

**Implementation**:
- Applies AI corrections to create updated code
- Deploys to PostgreSQL with error capture
- Uses psycopg2 for PostgreSQL connections
- Captures detailed error messages for next iteration

#### 10. Check Deployment Status (`deployment_status`)
**Purpose**: Determines workflow continuation based on deployment results

**Implementation**:
- Checks deployment success/failure status
- Ends workflow on successful deployment
- Prepares next iteration on deployment failure
- Tracks iterations across workflow runs

---

## Code Structure & Organization

### Directory Structure
```
QM_Conversion_Agent1/
├── config/                 # Configuration management
├── llm/                   # Language model integrations
├── state/                 # Pydantic state models
├── workflow/              # LangGraph workflow definition
├── nodes/                 # Migration workflow nodes
├── utils/                 # Utility functions
├── prompts/               # AI prompts for each workflow step
├── formatting/            # SQL formatting utilities
├── output_files/          # Generated workflow artifacts
├── main.py               # Main application entry point
└── streamlit_main.py     # Web interface
```

### Key Design Principles

#### 1. Pydantic-First Approach
- All data models use Pydantic for type safety
- Structured AI outputs ensure reliable parsing
- Comprehensive validation at data boundaries

#### 2. Function-Based Utilities
- Utility functions are normal functions, not static methods
- Clear separation of concerns
- Easy testing and maintenance

#### 3. Comprehensive Documentation
- Every function and class has detailed docstrings
- Clear parameter and return type documentation
- Usage examples where appropriate

#### 4. Error Handling
- Graceful error handling throughout the system
- Detailed error messages for debugging
- Fallback strategies for AI failures

---

## AI Integration & Pydantic Models

### Structured AI Outputs
The system uses Pydantic models to ensure reliable AI responses:

```python
class ValidationOutput(BaseModel):
    is_correct: bool = Field(description="Whether validation passed")
    explanation: str = Field(description="Detailed explanation")
    confidence: float = Field(description="Confidence score 0.0-1.0")
```

### Key Pydantic Models
- **WorkflowState**: Main state management for the workflow
- **CorrectedStatement**: AI-generated statement corrections
- **ValidationOutput**: AI validation results with confidence scoring
- **Phase1ErrorIdentificationOutput**: Error statement identification results
- **Phase2ErrorContextOutput**: Error context creation results

### AI Provider Integration
- Supports multiple LLM providers (OpenAI, Azure OpenAI, Anthropic, Groq, Gemini, Ollama)
- Consistent interface across all providers
- Structured outputs for reliable AI responses

### Confidence Scoring
- All AI operations include confidence scores (0.0-1.0)
- Enables quality assessment and decision making
- Supports iterative improvement strategies

---

## Development Guidelines

### Code Quality Standards
1. **Type Hints**: Use comprehensive type hints throughout
2. **Documentation**: Maintain detailed docstrings for all functions and classes
3. **Error Handling**: Implement robust error handling with graceful degradation
4. **Pydantic Models**: Use Pydantic for all data structures and AI outputs

### File Organization
1. **Pydantic Models**: Keep all models in `state/state.py`
2. **Prompts**: Store AI prompts in `prompts/` folder
3. **Utilities**: Use normal functions instead of static methods
4. **Documentation**: Maintain comprehensive docstrings and comments

### Adding New Features
1. **Define Pydantic Models**: Create structured models for new data types
2. **Update State**: Extend WorkflowState for new state requirements
3. **Create Prompts**: Develop specialized prompts for new AI operations
4. **Add Validation**: Implement validation for new workflow steps
5. **Update Documentation**: Maintain comprehensive documentation

### Best Practices
1. **Source Statement Reuse**: Optimize for iteration loops
2. **Position Mapping**: Use position-based resolution when possible
3. **Audit Trail**: Maintain complete file-based audit trail with Excel outputs
4. **Iteration Tracking**: Track workflow iterations for analysis
5. **Error Analysis**: Analyze deployment errors for patterns

This guide provides the foundation for understanding and developing with the QMigrator AI system. For specific implementation details, refer to the individual code files and their detailed documentation.
