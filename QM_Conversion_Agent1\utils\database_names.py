"""
Utility functions for dynamic database names in QMigrator AI.

This module provides helper functions to get dynamic database names from configuration
and use them throughout the application, making the system flexible for different
database migration scenarios.
"""

import os


def get_database_names():
    """Get source and target database names from environment variables."""
    source_db = os.getenv('SOURCE_DATABASE')
    target_db = os.getenv('TARGET_DATABASE')

    if not source_db:
        raise ValueError("SOURCE_DATABASE environment variable is required")
    if not target_db:
        raise ValueError("TARGET_DATABASE environment variable is required")

    return source_db, target_db


def get_database_specific_terms():
    """Get database-specific terminology for prompts."""
    source_db, target_db = get_database_names()

    return {
        'source_db': source_db,
        'target_db': target_db,
        'migration_direction': f"{source_db} to {target_db}",
        'expert_title': f"{source_db} to {target_db} Database Migration Expert"
    }
